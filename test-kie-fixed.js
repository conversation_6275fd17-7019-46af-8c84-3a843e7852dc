// 测试修复后的 kie.ai API 调用
const KIE_API_KEY = "4205bbf5a92b68ed52e37c735c6a8bb6";

async function testFixedKieAPI() {
  console.log('🔍 Testing FIXED kie.ai API endpoints...\n');
  
  const endpoints = [
    {
      name: "Fixed Endpoint 1",
      url: 'https://api.kie.ai/v1/predictions',
      body: {
        model: "google/nano-banana-edit",
        prompt: "change the background to a beach scene",
        image_urls: ["https://example.com/test.jpg"],
        num_images: "1"
      }
    },
    {
      name: "Fixed Endpoint 2",
      url: 'https://api.kie.ai/v1/models/google/nano-banana-edit/predictions',
      body: {
        input: {
          prompt: "change the background to a beach scene",
          image_urls: ["https://example.com/test.jpg"],
          num_images: "1"
        }
      }
    },
    {
      name: "Fixed Endpoint 3",
      url: 'https://api.kie.ai/predict',
      body: {
        model: "google/nano-banana-edit",
        input: {
          prompt: "change the background to a beach scene",
          image_urls: ["https://example.com/test.jpg"]
        }
      }
    },
    {
      name: "Fixed Endpoint 4",
      url: 'https://api.kie.ai/run/google/nano-banana-edit',
      body: {
        input: {
          prompt: "change the background to a beach scene",
          image_urls: ["https://example.com/test.jpg"]
        }
      }
    }
  ];

  for (const config of endpoints) {
    try {
      console.log(`\n🧪 Testing ${config.name}: ${config.url}`);
      console.log('📋 Request body:', JSON.stringify(config.body, null, 2));
      
      const response = await fetch(config.url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${KIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config.body),
        signal: AbortSignal.timeout(15000), // 15秒超时
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ SUCCESS! Response:`, JSON.stringify(data, null, 2));
        return { success: true, endpoint: config.url, response: data };
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText.substring(0, 200)}`);
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`   ⏱️ Timeout Error (15s): Request took too long`);
      } else {
        console.log(`   ❌ Network Error: ${error.message}`);
      }
    }
  }
  
  console.log('\n❌ All fixed endpoints failed');
  return { success: false };
}

testFixedKieAPI().then(result => {
  if (result.success) {
    console.log(`\n🎉 Found working endpoint: ${result.endpoint}`);
  } else {
    console.log('\n💡 Next steps:');
    console.log('1. Check if the google/nano-banana-edit model exists');
    console.log('2. Verify API key permissions for this model');
    console.log('3. Check kie.ai documentation for correct endpoint format');
    console.log('4. Consider if the model name has changed or is unavailable');
  }
});