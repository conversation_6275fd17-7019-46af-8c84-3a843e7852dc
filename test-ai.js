// 简单的AI功能测试
const { GoogleGenerativeAI } = require('@google/generative-ai');

// 使用环境变量中的API密钥
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || 'AIzaSyDUZbUUDh7yFKvZdpgD_i1ouba5RZJJ0Y0');

async function testAI() {
  try {
    console.log('🤖 开始测试AI功能...');
    
    // 获取模型
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    
    // 测试简单的对话
    const prompt = '你好，请用中文简单介绍一下你自己。';
    console.log('📝 发送提示:', prompt);
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ AI回复:', text);
    console.log('🎉 AI功能测试成功！');
    
  } catch (error) {
    console.error('❌ AI功能测试失败:', error.message);
    
    if (error.message.includes('API_KEY_INVALID')) {
      console.log('💡 提示: 请检查GEMINI_API_KEY环境变量是否正确设置');
    }
  }
}

// 运行测试
testAI();
