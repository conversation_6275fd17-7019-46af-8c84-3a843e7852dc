// Reset trial usage for testing
const postgres = require('postgres');

async function resetTrialUsage() {
  const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/saas_starter';
  const sql = postgres(connectionString);

  try {
    // Delete all trial usage records to reset everyone's trials
    await sql`DELETE FROM trial_usage`;
    console.log('✅ Trial usage reset successfully! All users now have 3 free trials.');
  } catch (error) {
    console.error('❌ Error resetting trial usage:', error);
    // If table doesn't exist, that's also fine - no usage to reset
    if (error.message.includes('relation "trial_usage" does not exist')) {
      console.log('💡 No trial_usage table found - no reset needed.');
    }
  } finally {
    await sql.end();
  }
}

resetTrialUsage();