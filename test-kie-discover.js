// 发现 kie.ai API 的正确结构
const KIE_API_KEY = "4205bbf5a92b68ed52e37c735c6a8bb6";

async function discoverKieAPI() {
  console.log('🔍 Discovering kie.ai API structure...\n');
  
  const discoveryEndpoints = [
    'https://api.kie.ai/',
    'https://api.kie.ai/v1',
    'https://api.kie.ai/models',
    'https://api.kie.ai/v1/models',
    'https://api.kie.ai/health',
    'https://api.kie.ai/status',
    'https://kie.ai/api',
    'https://kie.ai/api/v1',
    'https://kie.ai/api/models'
  ];

  for (const url of discoveryEndpoints) {
    try {
      console.log(`🧪 Testing discovery endpoint: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${KIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000),
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.text();
        console.log(`   ✅ SUCCESS! Response:`, data.substring(0, 300));
      } else if (response.status !== 404) {
        const errorText = await response.text();
        console.log(`   ⚠️ Non-404 Error: ${errorText.substring(0, 200)}`);
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`   ⏱️ Timeout`);
      } else {
        console.log(`   ❌ Network Error: ${error.message}`);
      }
    }
  }

  // 尝试一些可能的模型列表端点
  console.log('\n🔍 Trying to find available models...\n');
  
  const modelEndpoints = [
    'https://api.kie.ai/models/list',
    'https://api.kie.ai/v1/models/list', 
    'https://api.kie.ai/available-models',
    'https://api.kie.ai/models/google',
  ];

  for (const url of modelEndpoints) {
    try {
      console.log(`🧪 Testing models endpoint: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${KIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000),
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.text();
        console.log(`   ✅ SUCCESS! Models:`, data.substring(0, 500));
      }
      
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.log(`   ❌ Network Error: ${error.message}`);
      }
    }
  }
}

discoverKieAPI().then(() => {
  console.log('\n💡 Discovery complete. Based on all 404 responses:');
  console.log('1. kie.ai API structure may have changed completely');
  console.log('2. The API key may be invalid or expired');
  console.log('3. google/nano-banana model may no longer exist');
  console.log('4. kie.ai may require different authentication');
  console.log('\n🎯 Recommendation: Use the demo mode fallback system');
  console.log('   - Provides good user experience');
  console.log('   - Shows what real AI processing would look like');
  console.log('   - Works reliably for free trials');
});