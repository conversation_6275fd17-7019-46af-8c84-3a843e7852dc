"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const stripe_1 = require("../payments/stripe");
const drizzle_1 = require("./drizzle");
const schema_1 = require("./schema");
const session_1 = require("@/lib/auth/session");
async function createStripeProducts() {
    console.log('Creating Stripe products and prices...');
    const baseProduct = await stripe_1.stripe.products.create({
        name: 'Base',
        description: 'Base subscription plan',
    });
    await stripe_1.stripe.prices.create({
        product: baseProduct.id,
        unit_amount: 800, // $8 in cents
        currency: 'usd',
        recurring: {
            interval: 'month',
            trial_period_days: 7,
        },
    });
    const plusProduct = await stripe_1.stripe.products.create({
        name: 'Plus',
        description: 'Plus subscription plan',
    });
    await stripe_1.stripe.prices.create({
        product: plusProduct.id,
        unit_amount: 1200, // $12 in cents
        currency: 'usd',
        recurring: {
            interval: 'month',
            trial_period_days: 7,
        },
    });
    console.log('Stripe products and prices created successfully.');
}
async function seed() {
    const email = '<EMAIL>';
    const password = 'admin123';
    const passwordHash = await (0, session_1.hashPassword)(password);
    const [user] = await drizzle_1.db
        .insert(schema_1.users)
        .values([
        {
            email: email,
            passwordHash: passwordHash,
            role: "owner",
        },
    ])
        .returning();
    console.log('Initial user created.');
    const [team] = await drizzle_1.db
        .insert(schema_1.teams)
        .values({
        name: 'Test Team',
    })
        .returning();
    await drizzle_1.db.insert(schema_1.teamMembers).values({
        teamId: team.id,
        userId: user.id,
        role: 'owner',
    });
    await createStripeProducts();
}
seed()
    .catch((error) => {
    console.error('Seed process failed:', error);
    process.exit(1);
})
    .finally(() => {
    console.log('Seed process finished. Exiting...');
    process.exit(0);
});
