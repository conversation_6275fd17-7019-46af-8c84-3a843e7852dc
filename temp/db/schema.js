"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityType = exports.usageStatsRelations = exports.imageProcessesRelations = exports.activityLogsRelations = exports.teamMembersRelations = exports.invitationsRelations = exports.usersRelations = exports.teamsRelations = exports.usageStats = exports.imageProcesses = exports.invitations = exports.activityLogs = exports.teamMembers = exports.teams = exports.users = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_orm_1 = require("drizzle-orm");
exports.users = (0, pg_core_1.pgTable)('users', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    name: (0, pg_core_1.varchar)('name', { length: 100 }),
    email: (0, pg_core_1.varchar)('email', { length: 255 }).notNull().unique(),
    passwordHash: (0, pg_core_1.text)('password_hash').notNull(),
    role: (0, pg_core_1.varchar)('role', { length: 20 }).notNull().default('member'),
    createdAt: (0, pg_core_1.timestamp)('created_at').notNull().defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').notNull().defaultNow(),
    deletedAt: (0, pg_core_1.timestamp)('deleted_at'),
});
exports.teams = (0, pg_core_1.pgTable)('teams', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    name: (0, pg_core_1.varchar)('name', { length: 100 }).notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').notNull().defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').notNull().defaultNow(),
    stripeCustomerId: (0, pg_core_1.text)('stripe_customer_id').unique(),
    stripeSubscriptionId: (0, pg_core_1.text)('stripe_subscription_id').unique(),
    stripeProductId: (0, pg_core_1.text)('stripe_product_id'),
    planName: (0, pg_core_1.varchar)('plan_name', { length: 50 }),
    subscriptionStatus: (0, pg_core_1.varchar)('subscription_status', { length: 20 }),
});
exports.teamMembers = (0, pg_core_1.pgTable)('team_members', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    userId: (0, pg_core_1.integer)('user_id')
        .notNull()
        .references(() => exports.users.id),
    teamId: (0, pg_core_1.integer)('team_id')
        .notNull()
        .references(() => exports.teams.id),
    role: (0, pg_core_1.varchar)('role', { length: 50 }).notNull(),
    joinedAt: (0, pg_core_1.timestamp)('joined_at').notNull().defaultNow(),
});
exports.activityLogs = (0, pg_core_1.pgTable)('activity_logs', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    teamId: (0, pg_core_1.integer)('team_id')
        .notNull()
        .references(() => exports.teams.id),
    userId: (0, pg_core_1.integer)('user_id').references(() => exports.users.id),
    action: (0, pg_core_1.text)('action').notNull(),
    timestamp: (0, pg_core_1.timestamp)('timestamp').notNull().defaultNow(),
    ipAddress: (0, pg_core_1.varchar)('ip_address', { length: 45 }),
});
exports.invitations = (0, pg_core_1.pgTable)('invitations', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    teamId: (0, pg_core_1.integer)('team_id')
        .notNull()
        .references(() => exports.teams.id),
    email: (0, pg_core_1.varchar)('email', { length: 255 }).notNull(),
    role: (0, pg_core_1.varchar)('role', { length: 50 }).notNull(),
    invitedBy: (0, pg_core_1.integer)('invited_by')
        .notNull()
        .references(() => exports.users.id),
    invitedAt: (0, pg_core_1.timestamp)('invited_at').notNull().defaultNow(),
    status: (0, pg_core_1.varchar)('status', { length: 20 }).notNull().default('pending'),
});
// AI Image Processing Tables
exports.imageProcesses = (0, pg_core_1.pgTable)('image_processes', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    userId: (0, pg_core_1.integer)('user_id')
        .notNull()
        .references(() => exports.users.id),
    teamId: (0, pg_core_1.integer)('team_id').references(() => exports.teams.id),
    originalUrl: (0, pg_core_1.text)('original_url').notNull(),
    processedUrl: (0, pg_core_1.text)('processed_url'),
    prompt: (0, pg_core_1.text)('prompt').notNull(),
    status: (0, pg_core_1.varchar)('status', { length: 20 }).notNull().default('processing'),
    createdAt: (0, pg_core_1.timestamp)('created_at').notNull().defaultNow(),
});
exports.usageStats = (0, pg_core_1.pgTable)('usage_stats', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    userId: (0, pg_core_1.integer)('user_id')
        .notNull()
        .references(() => exports.users.id),
    teamId: (0, pg_core_1.integer)('team_id').references(() => exports.teams.id),
    featureType: (0, pg_core_1.varchar)('feature_type', { length: 50 }).notNull(),
    usageCount: (0, pg_core_1.integer)('usage_count').notNull().default(1),
    date: (0, pg_core_1.date)('date').notNull().defaultNow(),
});
exports.teamsRelations = (0, drizzle_orm_1.relations)(exports.teams, ({ many }) => ({
    teamMembers: many(exports.teamMembers),
    activityLogs: many(exports.activityLogs),
    invitations: many(exports.invitations),
    imageProcesses: many(exports.imageProcesses),
    usageStats: many(exports.usageStats),
}));
exports.usersRelations = (0, drizzle_orm_1.relations)(exports.users, ({ many }) => ({
    teamMembers: many(exports.teamMembers),
    invitationsSent: many(exports.invitations),
    imageProcesses: many(exports.imageProcesses),
    usageStats: many(exports.usageStats),
}));
exports.invitationsRelations = (0, drizzle_orm_1.relations)(exports.invitations, ({ one }) => ({
    team: one(exports.teams, {
        fields: [exports.invitations.teamId],
        references: [exports.teams.id],
    }),
    invitedBy: one(exports.users, {
        fields: [exports.invitations.invitedBy],
        references: [exports.users.id],
    }),
}));
exports.teamMembersRelations = (0, drizzle_orm_1.relations)(exports.teamMembers, ({ one }) => ({
    user: one(exports.users, {
        fields: [exports.teamMembers.userId],
        references: [exports.users.id],
    }),
    team: one(exports.teams, {
        fields: [exports.teamMembers.teamId],
        references: [exports.teams.id],
    }),
}));
exports.activityLogsRelations = (0, drizzle_orm_1.relations)(exports.activityLogs, ({ one }) => ({
    team: one(exports.teams, {
        fields: [exports.activityLogs.teamId],
        references: [exports.teams.id],
    }),
    user: one(exports.users, {
        fields: [exports.activityLogs.userId],
        references: [exports.users.id],
    }),
}));
exports.imageProcessesRelations = (0, drizzle_orm_1.relations)(exports.imageProcesses, ({ one }) => ({
    user: one(exports.users, {
        fields: [exports.imageProcesses.userId],
        references: [exports.users.id],
    }),
    team: one(exports.teams, {
        fields: [exports.imageProcesses.teamId],
        references: [exports.teams.id],
    }),
}));
exports.usageStatsRelations = (0, drizzle_orm_1.relations)(exports.usageStats, ({ one }) => ({
    user: one(exports.users, {
        fields: [exports.usageStats.userId],
        references: [exports.users.id],
    }),
    team: one(exports.teams, {
        fields: [exports.usageStats.teamId],
        references: [exports.teams.id],
    }),
}));
var ActivityType;
(function (ActivityType) {
    ActivityType["SIGN_UP"] = "SIGN_UP";
    ActivityType["SIGN_IN"] = "SIGN_IN";
    ActivityType["SIGN_OUT"] = "SIGN_OUT";
    ActivityType["UPDATE_PASSWORD"] = "UPDATE_PASSWORD";
    ActivityType["DELETE_ACCOUNT"] = "DELETE_ACCOUNT";
    ActivityType["UPDATE_ACCOUNT"] = "UPDATE_ACCOUNT";
    ActivityType["CREATE_TEAM"] = "CREATE_TEAM";
    ActivityType["REMOVE_TEAM_MEMBER"] = "REMOVE_TEAM_MEMBER";
    ActivityType["INVITE_TEAM_MEMBER"] = "INVITE_TEAM_MEMBER";
    ActivityType["ACCEPT_INVITATION"] = "ACCEPT_INVITATION";
    ActivityType["AI_IMAGE_PROCESS"] = "AI_IMAGE_PROCESS";
    ActivityType["AI_IMAGE_UPLOAD"] = "AI_IMAGE_UPLOAD";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
