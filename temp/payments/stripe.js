"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripe = void 0;
exports.createCheckoutSession = createCheckoutSession;
exports.createCustomerPortalSession = createCustomerPortalSession;
exports.handleSubscriptionChange = handleSubscriptionChange;
exports.getStripePrices = getStripePrices;
exports.getStripeProducts = getStripeProducts;
const stripe_1 = __importDefault(require("stripe"));
const navigation_1 = require("next/navigation");
const queries_1 = require("@/lib/db/queries");
exports.stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-04-30.basil'
});
async function createCheckoutSession({ team, priceId }) {
    const user = await (0, queries_1.getUser)();
    if (!team || !user) {
        (0, navigation_1.redirect)(`/sign-up?redirect=checkout&priceId=${priceId}`);
    }
    const session = await exports.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
            {
                price: priceId,
                quantity: 1
            }
        ],
        mode: 'subscription',
        success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.BASE_URL}/pricing`,
        customer: team.stripeCustomerId || undefined,
        client_reference_id: user.id.toString(),
        allow_promotion_codes: true,
        subscription_data: {
            trial_period_days: 14
        }
    });
    (0, navigation_1.redirect)(session.url);
}
async function createCustomerPortalSession(team) {
    if (!team.stripeCustomerId || !team.stripeProductId) {
        (0, navigation_1.redirect)('/pricing');
    }
    let configuration;
    const configurations = await exports.stripe.billingPortal.configurations.list();
    if (configurations.data.length > 0) {
        configuration = configurations.data[0];
    }
    else {
        const product = await exports.stripe.products.retrieve(team.stripeProductId);
        if (!product.active) {
            throw new Error("Team's product is not active in Stripe");
        }
        const prices = await exports.stripe.prices.list({
            product: product.id,
            active: true
        });
        if (prices.data.length === 0) {
            throw new Error("No active prices found for the team's product");
        }
        configuration = await exports.stripe.billingPortal.configurations.create({
            business_profile: {
                headline: 'Manage your subscription'
            },
            features: {
                subscription_update: {
                    enabled: true,
                    default_allowed_updates: ['price', 'quantity', 'promotion_code'],
                    proration_behavior: 'create_prorations',
                    products: [
                        {
                            product: product.id,
                            prices: prices.data.map((price) => price.id)
                        }
                    ]
                },
                subscription_cancel: {
                    enabled: true,
                    mode: 'at_period_end',
                    cancellation_reason: {
                        enabled: true,
                        options: [
                            'too_expensive',
                            'missing_features',
                            'switched_service',
                            'unused',
                            'other'
                        ]
                    }
                },
                payment_method_update: {
                    enabled: true
                }
            }
        });
    }
    return exports.stripe.billingPortal.sessions.create({
        customer: team.stripeCustomerId,
        return_url: `${process.env.BASE_URL}/dashboard`,
        configuration: configuration.id
    });
}
async function handleSubscriptionChange(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;
    const status = subscription.status;
    const team = await (0, queries_1.getTeamByStripeCustomerId)(customerId);
    if (!team) {
        console.error('Team not found for Stripe customer:', customerId);
        return;
    }
    if (status === 'active' || status === 'trialing') {
        const plan = subscription.items.data[0]?.plan;
        await (0, queries_1.updateTeamSubscription)(team.id, {
            stripeSubscriptionId: subscriptionId,
            stripeProductId: plan?.product,
            planName: (plan?.product).name,
            subscriptionStatus: status
        });
    }
    else if (status === 'canceled' || status === 'unpaid') {
        await (0, queries_1.updateTeamSubscription)(team.id, {
            stripeSubscriptionId: null,
            stripeProductId: null,
            planName: null,
            subscriptionStatus: status
        });
    }
}
async function getStripePrices() {
    const prices = await exports.stripe.prices.list({
        expand: ['data.product'],
        active: true,
        type: 'recurring'
    });
    return prices.data.map((price) => ({
        id: price.id,
        productId: typeof price.product === 'string' ? price.product : price.product.id,
        unitAmount: price.unit_amount,
        currency: price.currency,
        interval: price.recurring?.interval,
        trialPeriodDays: price.recurring?.trial_period_days
    }));
}
async function getStripeProducts() {
    const products = await exports.stripe.products.list({
        active: true,
        expand: ['data.default_price']
    });
    return products.data.map((product) => ({
        id: product.id,
        name: product.name,
        description: product.description,
        defaultPriceId: typeof product.default_price === 'string'
            ? product.default_price
            : product.default_price?.id
    }));
}
