// 测试 kie.ai API 连接
const KIE_API_KEY = "4205bbf5a92b68ed52e37c735c6a8bb6";

async function testKieAPI() {
  const testImage = "https://example.com/test.jpg"; // 测试图片URL
  const testPrompt = "Change the background to a beach scene";

  const endpoints = [
    {
      name: "Endpoint 1",
      url: 'https://api.kie.ai/predict',
      body: {
        model: "google/nano-banana",
        input: {
          prompt: testPrompt,
          image: testImage
        }
      }
    },
    {
      name: "Endpoint 2", 
      url: 'https://kie.ai/api/predict',
      body: {
        model: "google/nano-banana",
        prompt: testPrompt,
        image: testImage
      }
    },
    {
      name: "Endpoint 3",
      url: 'https://api.kie.ai/v1/generate',
      body: {
        model_name: "google/nano-banana",
        prompt: testPrompt,
        image_url: testImage
      }
    },
    {
      name: "Endpoint 4",
      url: 'https://kie.ai/api/run/google/nano-banana',
      body: {
        input: {
          prompt: testPrompt,
          image: testImage
        }
      }
    },
    {
      name: "Endpoint 5",
      url: 'https://api.kie.ai/run',
      body: {
        model: "google/nano-banana",
        input: {
          prompt: testPrompt,
          image: testImage
        }
      }
    }
  ];

  for (const config of endpoints) {
    try {
      console.log(`\n🧪 Testing ${config.name}: ${config.url}`);
      
      const response = await fetch(config.url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${KIE_API_KEY}`,
          'Content-Type': 'application/json',
          'X-API-Key': KIE_API_KEY,
        },
        body: JSON.stringify(config.body),
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ SUCCESS! Response:`, JSON.stringify(data, null, 2));
        return { success: true, endpoint: config.url, response: data };
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Network Error: ${error.message}`);
    }
  }
  
  console.log('\n❌ All endpoints failed');
  return { success: false };
}

testKieAPI().then(result => {
  if (result.success) {
    console.log(`\n🎉 Found working endpoint: ${result.endpoint}`);
  } else {
    console.log('\n💡 Suggestions:');
    console.log('1. Check if API key is valid');
    console.log('2. Verify kie.ai model availability');
    console.log('3. Check kie.ai documentation for correct endpoints');
  }
});