#!/usr/bin/env node

/**
 * 测试KIE API密钥是否正常工作
 * 运行方式: node scripts/test-kie-api.js
 */

require('dotenv').config();

const KIE_API_KEY = process.env.KIE_API_KEY;

if (!KIE_API_KEY) {
  console.error('❌ KIE_API_KEY not found in environment variables');
  process.exit(1);
}

console.log('🔑 KIE API Key found:', KIE_API_KEY.substring(0, 8) + '...');

async function testKieAPI() {
  try {
    console.log('🧪 Testing KIE API connection...');
    
    // 使用一个测试图片URL
    const testImageUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400';
    const testPrompt = 'Change the background to a beautiful sunset';

    const response = await fetch('https://api.kie.ai/google/nano-banana-edit', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${KIE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: testPrompt,
        image_urls: [testImageUrl],
        num_images: 1
      }),
    });

    console.log('📡 API Response Status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return false;
    }

    const result = await response.json();
    console.log('✅ API Response received');
    console.log('📊 Response structure:', {
      hasOutput: !!result.output,
      outputType: Array.isArray(result.output) ? 'array' : typeof result.output,
      outputLength: Array.isArray(result.output) ? result.output.length : 'N/A'
    });

    if (result.output && Array.isArray(result.output) && result.output.length > 0) {
      console.log('🎉 Test successful! Nano Banana API is working');
      console.log('🖼️  Generated image URL:', result.output[0]);
      return true;
    } else {
      console.error('❌ Unexpected response format:', result);
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🍌 KIE Nano Banana API Test');
  console.log('=' .repeat(50));
  
  const success = await testKieAPI();
  
  console.log('=' .repeat(50));
  if (success) {
    console.log('✅ All tests passed! Your KIE API key is working correctly.');
    console.log('🚀 Your AI image editing platform is ready to use nano-banana!');
  } else {
    console.log('❌ Tests failed. Please check your KIE API key and try again.');
    console.log('🔧 Make sure your API key is valid and has sufficient credits.');
  }
}

main().catch(console.error);
