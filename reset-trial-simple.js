// Simple trial reset using environment variables
require('dotenv').config();
const postgres = require('postgres');

async function resetTrials() {
  const sql = postgres(process.env.POSTGRES_URL);
  
  try {
    await sql`DELETE FROM trial_usage`;
    console.log('✅ Trial usage reset successfully!');
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await sql.end();
  }
}

resetTrials();