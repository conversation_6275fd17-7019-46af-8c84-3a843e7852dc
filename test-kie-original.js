// 测试原始的 google/nano-banana 模型
const KIE_API_KEY = "4205bbf5a92b68ed52e37c735c6a8bb6";

async function testOriginalNanoBanana() {
  console.log('🔍 Testing ORIGINAL google/nano-banana model...\n');
  
  const endpoints = [
    {
      name: "Original Format 1",
      url: 'https://api.kie.ai/v1/predictions',
      body: {
        model: "google/nano-banana",
        prompt: "change the background to a beach scene",
        image_urls: ["https://example.com/test.jpg"],
        num_images: "1"
      }
    },
    {
      name: "Original Format 2",
      url: 'https://api.kie.ai/v1/predictions',
      body: {
        version: "google/nano-banana",
        input: {
          prompt: "change the background to a beach scene",
          image: "https://example.com/test.jpg"
        }
      }
    },
    {
      name: "Original Format 3", 
      url: 'https://api.kie.ai/v1/models/google/nano-banana/predictions',
      body: {
        input: {
          prompt: "change the background to a beach scene",
          image_urls: ["https://example.com/test.jpg"]
        }
      }
    },
    {
      name: "Direct Model Call",
      url: 'https://api.kie.ai/run/google/nano-banana',
      body: {
        input: {
          prompt: "change the background to a beach scene",
          image: "https://example.com/test.jpg"
        }
      }
    }
  ];

  for (const config of endpoints) {
    try {
      console.log(`\n🧪 Testing ${config.name}: ${config.url}`);
      console.log('📋 Request body:', JSON.stringify(config.body, null, 2));
      
      const response = await fetch(config.url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${KIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config.body),
        signal: AbortSignal.timeout(15000),
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ SUCCESS! Response:`, JSON.stringify(data, null, 2));
        return { success: true, endpoint: config.url, response: data };
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText.substring(0, 200)}`);
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`   ⏱️ Timeout Error (15s): Request took too long`);
      } else {
        console.log(`   ❌ Network Error: ${error.message}`);
      }
    }
  }
  
  console.log('\n❌ All original model endpoints failed');
  return { success: false };
}

testOriginalNanoBanana().then(result => {
  if (result.success) {
    console.log(`\n🎉 Found working endpoint: ${result.endpoint}`);
  } else {
    console.log('\n💡 Conclusion:');
    console.log('- google/nano-banana model appears unavailable');
    console.log('- google/nano-banana-edit model does not exist');
    console.log('- The API key might not have access to these models');
    console.log('- kie.ai service might have changed their API structure');
  }
});