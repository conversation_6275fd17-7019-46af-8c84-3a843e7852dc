# 🚀 AI Image Edit SaaS 部署指南

## 📋 部署前检查清单

### 必需的账户和服务
- [ ] GitHub账户 (代码托管)
- [ ] Vercel账户 (网站部署)
- [ ] Stripe账户 (支付处理)
- [ ] Supabase/Neon账户 (数据库)
- [ ] 域名 (可选，推荐)

## 🗄️ 第一步：设置生产数据库

### 选项A: Supabase (推荐)
1. 访问 [supabase.com](https://supabase.com)
2. 创建新项目
3. 获取数据库连接字符串
4. 格式：`postgresql://postgres:[password]@[host]:5432/postgres`

### 选项B: Neon
1. 访问 [neon.tech](https://neon.tech)
2. 创建新项目
3. 获取连接字符串

## 💳 第二步：配置Stripe

### 1. 创建Stripe产品
```bash
# 登录Stripe Dashboard
# 创建3个产品：
- Free Plan (免费)
- Pro Plan ($29/月)
- Enterprise Plan ($99/月)
```

### 2. 获取API密钥
- 测试密钥：`sk_test_...`
- 生产密钥：`sk_live_...`
- Webhook密钥：`whsec_...`

### 3. 设置Webhook
- 端点URL：`https://yourdomain.com/api/stripe/webhook`
- 监听事件：
  - `checkout.session.completed`
  - `customer.subscription.updated`
  - `customer.subscription.deleted`

## 🌐 第三步：部署到Vercel

### 1. 连接GitHub
```bash
# 推送代码到GitHub
git add .
git commit -m "Ready for production deployment"
git push origin main
```

### 2. 在Vercel中导入项目
1. 访问 [vercel.com](https://vercel.com)
2. 点击 "New Project"
3. 导入您的GitHub仓库
4. 配置环境变量

### 3. 环境变量配置
```env
# 数据库
POSTGRES_URL=postgresql://...

# Stripe
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# 应用配置
BASE_URL=https://yourdomain.com
AUTH_SECRET=your-random-secret-key

# 生成AUTH_SECRET
openssl rand -base64 32
```

## 🔧 第四步：数据库初始化

### 在Vercel部署后运行：
```bash
# 在Vercel Functions中运行迁移
# 或者本地连接生产数据库运行：
pnpm db:migrate
pnpm db:seed
```

## 🌍 第五步：域名配置 (可选)

### 1. 购买域名
推荐平台：
- Namecheap
- GoDaddy
- Cloudflare

### 2. 在Vercel中添加域名
1. 项目设置 → Domains
2. 添加自定义域名
3. 配置DNS记录

## 📊 第六步：基础监控设置

### 1. Google Analytics
```html
<!-- 添加到 app/layout.tsx -->
<Script src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID" />
```

### 2. Vercel Analytics
```bash
# 在Vercel Dashboard中启用
Project Settings → Analytics → Enable
```

## 🧪 第七步：测试部署

### 测试清单
- [ ] 主页加载正常
- [ ] 用户注册/登录
- [ ] Stripe支付流程
- [ ] 仪表板功能
- [ ] 移动端响应式
- [ ] SEO元数据

## 🚀 第八步：上线准备

### 1. 内容准备
- [ ] 更新公司信息
- [ ] 添加隐私政策
- [ ] 添加服务条款
- [ ] 设置客服邮箱

### 2. 营销准备
- [ ] 社交媒体账户
- [ ] 邮件营销工具
- [ ] 客服系统

## 📈 预期时间线

| 阶段 | 时间 | 任务 |
|------|------|------|
| **第1天** | 2-4小时 | 数据库和Stripe配置 |
| **第2天** | 1-2小时 | Vercel部署和域名 |
| **第3天** | 2-3小时 | 测试和优化 |
| **第4-7天** | 按需 | 内容和营销准备 |

## 🎯 成功指标

部署成功后，您将拥有：
- ✅ 完全功能的SaaS网站
- ✅ 用户注册和支付系统
- ✅ 专业的品牌形象
- ✅ SEO优化的营销页面
- ✅ 可扩展的技术架构

## 🔄 下一步：MVP功能开发

部署完成后，可以开始开发核心AI功能：
1. 集成AI图像处理API
2. 构建简单的编辑界面
3. 添加用户使用限制
4. 收集用户反馈

---

**准备好开始了吗？让我知道您想从哪一步开始！**
