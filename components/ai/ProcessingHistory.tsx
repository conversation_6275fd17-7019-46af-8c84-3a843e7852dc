'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Download, Eye, Trash2, RefreshCw, AlertCircle } from 'lucide-react';
import { User } from '@/lib/db/schema';
// 简单的时间格式化函数，替代 date-fns
const formatDistanceToNow = (date: Date) => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffDay > 0) return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  if (diffHour > 0) return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  if (diffMin > 0) return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  return 'just now';
};

interface ProcessingHistoryProps {
  user: User;
}

interface ProcessRecord {
  id: number;
  originalUrl: string;
  processedUrl: string | null;
  operation: {
    operation: string;
    parameters?: Record<string, any>;
  };
  status: string;
  createdAt: string;
}

const getOperationName = (operation: string) => {
  const names: Record<string, string> = {
    'remove-background': 'Remove Background',
    'replace-background': 'Replace Background',
    'enhance-image': 'Enhance Quality',
    'multi-angle-portrait': 'Multi-Angle Portrait',
    'style-transfer': 'Style Transfer',
  };
  return names[operation] || operation;
};

const getOperationIcon = (operation: string) => {
  const icons: Record<string, string> = {
    'remove-background': '🎭',
    'replace-background': '🌅',
    'enhance-image': '✨',
    'multi-angle-portrait': '👤',
    'style-transfer': '🎨',
  };
  return icons[operation] || '🔧';
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'default';
    case 'processing':
      return 'secondary';
    case 'failed':
      return 'destructive';
    default:
      return 'outline';
  }
};

export default function ProcessingHistory({ user }: ProcessingHistoryProps) {
  const [processes, setProcesses] = useState<ProcessRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/ai/process');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch history');
      }

      if (data.success) {
        setProcesses(data.processes || []);
      }
    } catch (error) {
      console.error('Fetch history error:', error);
      setError(error instanceof Error ? error.message : 'Failed to load history');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  const downloadImage = useCallback(async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  }, []);

  const viewImage = useCallback((imageUrl: string) => {
    window.open(imageUrl, '_blank');
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Loading history...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          {error}
          <Button variant="ghost" size="sm" onClick={fetchHistory}>
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (processes.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
          <Eye className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">No Processing History</h3>
        <p className="text-muted-foreground">
          Your AI image processing history will appear here once you start using the editor.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Recent Processes ({processes.length})</h3>
        <Button variant="outline" size="sm" onClick={fetchHistory}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid gap-4">
        {processes.map((process) => (
          <Card key={process.id}>
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                {/* Images */}
                <div className="flex gap-2">
                  {/* Original Image */}
                  <div className="relative">
                    <img
                      src={process.originalUrl}
                      alt="Original"
                      className="w-16 h-16 object-cover rounded border"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute -top-1 -right-1 h-6 w-6 p-0 bg-background border"
                      onClick={() => viewImage(process.originalUrl)}
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center justify-center w-8 h-16">
                    <span className="text-muted-foreground">→</span>
                  </div>

                  {/* Processed Image */}
                  <div className="relative">
                    {process.processedUrl ? (
                      <>
                        <img
                          src={process.processedUrl}
                          alt="Processed"
                          className="w-16 h-16 object-cover rounded border"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute -top-1 -right-1 h-6 w-6 p-0 bg-background border"
                          onClick={() => viewImage(process.processedUrl!)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      </>
                    ) : (
                      <div className="w-16 h-16 border-2 border-dashed rounded flex items-center justify-center">
                        <span className="text-xs text-muted-foreground">N/A</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">
                      {getOperationIcon(process.operation.operation)}
                    </span>
                    <h4 className="font-medium">
                      {getOperationName(process.operation.operation)}
                    </h4>
                    <Badge variant={getStatusColor(process.status)}>
                      {process.status}
                    </Badge>
                  </div>

                  <p className="text-sm text-muted-foreground mb-2">
                    {formatDistanceToNow(new Date(process.createdAt), { addSuffix: true })}
                  </p>

                  {/* Parameters */}
                  {process.operation.parameters && Object.keys(process.operation.parameters).length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      {Object.entries(process.operation.parameters).map(([key, value]) => (
                        <span key={key} className="mr-2">
                          {key}: {String(value)}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-col gap-1">
                  {process.processedUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadImage(process.processedUrl!, `processed-${process.id}.jpg`)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
