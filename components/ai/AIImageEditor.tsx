'use client';

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Download, Loader2, Wand2, Image as ImageIcon } from 'lucide-react';
import ImageUpload from './ImageUpload';
import ImageProcessor from './ImageProcessor';
import TextEditProcessor from './TextEditProcessor';
import ProcessingHistory from './ProcessingHistory';
import { User } from '@/lib/db/schema';

interface AIImageEditorProps {
  user: User;
}

export default function AIImageEditor({ user }: AIImageEditorProps) {
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');

  const handleImageUpload = useCallback((imageUrl: string) => {
    setCurrentImage(imageUrl);
    setProcessedImage(null);
    setActiveTab('process');
  }, []);

  const handleImageProcessed = useCallback((result: string) => {
    setProcessedImage(result);
    setIsProcessing(false);
  }, []);

  const handleProcessingStart = useCallback(() => {
    setIsProcessing(true);
  }, []);

  const handleProcessingError = useCallback(() => {
    setIsProcessing(false);
  }, []);

  const downloadImage = useCallback(async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  }, []);

  return (
    <div className="w-full max-w-7xl mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            上传图片
          </TabsTrigger>
          <TabsTrigger value="text-edit" className="flex items-center gap-2" disabled={!currentImage}>
            <Wand2 className="h-4 w-4" />
            文字编辑
          </TabsTrigger>
          <TabsTrigger value="process" className="flex items-center gap-2" disabled={!currentImage}>
            <ImageIcon className="h-4 w-4" />
            其他功能
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            历史记录
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>上传图片</CardTitle>
              <CardDescription>
                上传图片开始AI编辑。支持格式：JPG, PNG, GIF, WebP
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUpload onImageUpload={handleImageUpload} user={user} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="text-edit" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 原图显示 */}
            <Card>
              <CardHeader>
                <CardTitle>原始图片</CardTitle>
              </CardHeader>
              <CardContent>
                {currentImage ? (
                  <div className="relative">
                    <img
                      src={currentImage}
                      alt="Original"
                      className="w-full h-auto rounded-lg border"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => downloadImage(currentImage, 'original.jpg')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <p className="text-muted-foreground">请先上传图片</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 处理结果 */}
            <Card>
              <CardHeader>
                <CardTitle>编辑结果</CardTitle>
              </CardHeader>
              <CardContent>
                {isProcessing ? (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">正在处理您的图片...</p>
                    </div>
                  </div>
                ) : processedImage ? (
                  <div className="relative">
                    <img
                      src={processedImage}
                      alt="Processed"
                      className="w-full h-auto rounded-lg border"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => downloadImage(processedImage, 'text-edited.jpg')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <p className="text-muted-foreground">编辑后的图片将显示在这里</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 文字编辑控制面板 */}
          {currentImage && (
            <Card>
              <CardHeader>
                <CardTitle>AI文字编辑</CardTitle>
                <CardDescription>
                  智能编辑图片中的文字，支持替换、翻译、修正等多种操作
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TextEditProcessor
                  imageUrl={currentImage}
                  onProcessingStart={handleProcessingStart}
                  onProcessingComplete={handleImageProcessed}
                  onProcessingError={handleProcessingError}
                  user={user}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="process" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Original Image */}
            <Card>
              <CardHeader>
                <CardTitle>Original Image</CardTitle>
              </CardHeader>
              <CardContent>
                {currentImage ? (
                  <div className="relative">
                    <img
                      src={currentImage}
                      alt="Original"
                      className="w-full h-auto rounded-lg border"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => downloadImage(currentImage, 'original.jpg')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <p className="text-muted-foreground">No image uploaded</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Processed Image */}
            <Card>
              <CardHeader>
                <CardTitle>Processed Image</CardTitle>
              </CardHeader>
              <CardContent>
                {isProcessing ? (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Processing your image...</p>
                    </div>
                  </div>
                ) : processedImage ? (
                  <div className="relative">
                    <img
                      src={processedImage}
                      alt="Processed"
                      className="w-full h-auto rounded-lg border"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => downloadImage(processedImage, 'processed.jpg')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed rounded-lg">
                    <p className="text-muted-foreground">Processed image will appear here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Processing Controls */}
          {currentImage && (
            <Card>
              <CardHeader>
                <CardTitle>AI Processing Tools</CardTitle>
                <CardDescription>
                  Choose an AI operation to transform your image
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageProcessor
                  imageUrl={currentImage}
                  onProcessingStart={handleProcessingStart}
                  onProcessingComplete={handleImageProcessed}
                  onProcessingError={handleProcessingError}
                  user={user}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Processing History</CardTitle>
              <CardDescription>
                View your recent AI image processing history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProcessingHistory user={user} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
