'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';

interface DemoImageEffectProps {
  src: string;
  alt: string;
  className?: string;
  onClick?: () => void;
}

export default function DemoImageEffect({ src, alt, className = '', onClick }: DemoImageEffectProps) {
  const [isProcessed, setIsProcessed] = useState(false);
  const [effect, setEffect] = useState('');
  const [operation, setOperation] = useState('');
  const [description, setDescription] = useState('');

  useEffect(() => {
    try {
      const url = new URL(src);
      const isDemo = url.searchParams.get('demo');
      const urlOperation = url.searchParams.get('operation');
      const urlEffect = url.searchParams.get('effect');
      const urlDescription = url.searchParams.get('desc');
      
      if (isDemo === 'enhanced') {
        setIsProcessed(true);
        setOperation(urlOperation || '');
        setDescription(urlDescription ? decodeURIComponent(urlDescription) : 'AI Enhanced');
        
        if (urlEffect) {
          // Use the specific effect from the API
          setEffect(decodeURIComponent(urlEffect));
        } else if (urlOperation) {
          // Fallback to default effects
          switch (urlOperation) {
            case 'background-replace':
              setEffect('hue-rotate(30deg) saturate(1.3) brightness(1.05)');
              break;
            case 'text-edit':
              setEffect('contrast(1.2) brightness(1.1) saturate(1.1)');
              break;
            case 'enhance-face':
              setEffect('brightness(1.08) saturate(1.15) contrast(1.05)');
              break;
            default:
              setEffect('saturate(1.2) brightness(1.05)');
          }
        }
      }
    } catch (error) {
      console.error('Error parsing image URL:', error);
    }
  }, [src]);

  const baseImageUrl = src.split('?')[0]; // Remove query parameters for actual image

  return (
    <div className={`relative overflow-hidden ${className}`} onClick={onClick}>
      <img
        src={baseImageUrl}
        alt={alt}
        style={{
          filter: isProcessed ? effect : 'none',
          transition: 'filter 0.3s ease-in-out',
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
      />
      
      {/* Processing indicator overlay */}
      {isProcessed && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Subtle overlay to indicate processing */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-400/10 to-orange-400/10 animate-pulse" />
          
          {/* Processing badge */}
          <div className="absolute top-2 right-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
            ✨ {description || 'AI Enhanced'}
          </div>
        </div>
      )}
    </div>
  );
}