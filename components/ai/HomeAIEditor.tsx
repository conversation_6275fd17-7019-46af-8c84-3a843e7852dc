'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, Sparkles, Wand2, Download, Loader2, AlertCircle, Clock, Crown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import DemoImageEffect from './DemoImageEffect';

interface HomeAIEditorProps {
  user?: any; // 用户信息，如果未登录则为undefined
}

interface TrialInfo {
  remaining: number;
  dailyLimit: number;
  resetTime?: string;
}

export default function HomeAIEditor({ user }: HomeAIEditorProps) {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [prompt, setPrompt] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [trialInfo, setTrialInfo] = useState<TrialInfo | null>(null);
  const [isLoadingTrial, setIsLoadingTrial] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // 获取试用信息
  const fetchTrialInfo = useCallback(async () => {
    if (user) return; // 已登录用户不需要试用信息
    
    setIsLoadingTrial(true);
    try {
      const response = await fetch('/api/ai/trial-process', {
        method: 'GET',
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.trial) {
          setTrialInfo({
            remaining: data.trial.remaining,
            dailyLimit: data.trial.dailyLimit,
            resetTime: data.trial.resetTime
          });
        }
      }
    } catch (error) {
      console.error('Failed to get trial info:', error);
    } finally {
      setIsLoadingTrial(false);
    }
  }, [user]);

  // 组件加载时获取试用信息
  useEffect(() => {
    fetchTrialInfo();
  }, [fetchTrialInfo]);

  // 处理文件上传
  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    if (file.size > 20 * 1024 * 1024) { // 20MB
      setError('File size cannot exceed 20MB');
      return;
    }

    setImageFile(file);
    setError(null);
    setProcessedImage(null);

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // 处理拖拽上传
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // 智能AI处理 - 自动判断使用哪个模型
  const handleAIEdit = useCallback(async () => {
    if (!imageFile || !prompt.trim()) {
      setError('Please upload an image and enter editing instructions');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // 首先上传图片
      const formData = new FormData();
      formData.append('image', imageFile);

      // 根据用户登录状态选择上传API
      const uploadEndpoint = user ? '/api/ai/upload' : '/api/ai/trial-upload';
      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Image upload failed');
      }

      const uploadData = await uploadResponse.json();

      if (!uploadData.success) {
        throw new Error(uploadData.error || 'Image upload failed');
      }

      // 根据用户登录状态选择处理API
      const processEndpoint = user ? '/api/ai/nano-banana-unified' : '/api/ai/trial-process';
      const processResponse = await fetch(processEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: uploadData.imageUrl,
          prompt: prompt.trim(),
        }),
      });

      const processData = await processResponse.json();
      console.log('Process response data:', processData);

      if (!processResponse.ok) {
        // 处理试用次数用完的情况
        if (processData.trialLimitReached || processData.trialExhausted) {
          setShowLoginPrompt(true);
          setError(processData.message || 'Free trial uses exhausted. Please sign up for more uses.');
          return;
        }
        throw new Error(processData.error || 'AI processing failed');
      }

      if (processData.success && processData.result) {
        console.log('Setting processed image:', processData.result);
        setProcessedImage(processData.result);

        // 如果是试用用户，更新剩余次数
        if (processData.trial && !user) {
          setTrialInfo({
            remaining: processData.trial.remaining,
            dailyLimit: processData.trial.dailyLimit,
            resetTime: processData.trial.resetTime
          });
          console.log(`Trial remaining: ${processData.trial.remaining}`);
        }
      } else {
        throw new Error('No processing result returned');
      }
    } catch (error) {
      console.error('AI edit error:', error);
      setError(error instanceof Error ? error.message : 'AI editing failed');
    } finally {
      setIsProcessing(false);
    }
  }, [imageFile, prompt, user]);

  // 下载图片
  const handleDownload = useCallback(async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  }, []);

  // 快速示例点击
  const handleExampleClick = useCallback((example: string) => {
    setPrompt(example);
  }, []);

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-amber-200/50 shadow-2xl">
      
      {/* 试用次数显示 */}
      {!user && (
        <div className="mb-6">
          {isLoadingTrial ? (
            <div className="flex items-center gap-2 px-4 py-2 bg-stone-100 rounded-xl">
              <Loader2 className="h-4 w-4 animate-spin text-stone-500" />
              <span className="text-stone-600 text-sm">Loading...</span>
            </div>
          ) : trialInfo ? (
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-stone-800">
                    Free Trial - {trialInfo.remaining}/{trialInfo.dailyLimit} uses remaining
                  </div>
                  <div className="text-sm text-stone-600">
                    Resets daily, upgrade for unlimited uses
                  </div>
                </div>
              </div>
              <Button 
                size="sm" 
                onClick={() => router.push('/sign-up')}
                className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white"
              >
                <Crown className="w-4 h-4 mr-1" />
                Upgrade
              </Button>
            </div>
          ) : null}
          
          {/* Trial exhausted notification */}
          {trialInfo && trialInfo.remaining === 0 && (
            <div className="mt-3 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-2xl">
              <div className="flex items-center gap-2 text-red-700 mb-2">
                <Clock className="w-4 h-4" />
                <span className="font-semibold">Daily trial uses exhausted</span>
              </div>
              <p className="text-red-600 text-sm mb-3">
                Sign up for more free trials, or upgrade to professional plan for unlimited use
              </p>
              <div className="flex gap-2">
                <Button 
                  size="sm"
                  onClick={() => router.push('/sign-up')}
                  className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700"
                >
                  Sign Up Free
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => router.push('/pricing')}
                >
                  View Plans
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8 mb-8">
        {/* 图片上传区域 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-stone-800 flex items-center gap-2">
            <Upload className="w-5 h-5 text-amber-600" />
            Upload for AI Image Edit
          </h3>
          <div 
            className="aspect-square bg-gradient-to-br from-stone-100 to-stone-200 rounded-2xl flex items-center justify-center border-2 border-dashed border-stone-300 hover:border-amber-400 transition-colors duration-300 cursor-pointer group relative overflow-hidden"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            {imagePreview ? (
              <img
                src={imagePreview}
                alt="Uploaded image"
                className="w-full h-full object-cover rounded-2xl"
              />
            ) : (
              <div className="text-center">
                <Upload className="w-12 h-12 text-stone-400 group-hover:text-amber-600 mx-auto mb-4 transition-colors duration-300" />
                <p className="text-lg text-stone-600 group-hover:text-stone-800 mb-2 transition-colors duration-300">
                  Drop your image for AI image edit
                </p>
                <p className="text-sm text-stone-500">JPG, PNG, WebP • Max 20MB for AI image edit</p>
                <Button className="mt-4 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600">
                  Choose File
                </Button>
              </div>
            )}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
              className="hidden"
            />
          </div>
        </div>

        {/* AI处理结果区域 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-stone-800 flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-amber-600" />
            AI Image Edit Result
          </h3>
          <div className="aspect-square bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl flex items-center justify-center border border-amber-200 relative overflow-hidden">
            {isProcessing ? (
              <div className="text-center">
                <Loader2 className="w-12 h-12 text-amber-600 animate-spin mx-auto mb-4" />
                <p className="text-lg text-stone-600 mb-2">AI magic is happening...</p>
                <p className="text-sm text-stone-500">Please wait, this may take a few seconds</p>
              </div>
            ) : processedImage ? (
              <>
                <DemoImageEffect
                  src={processedImage}
                  alt="AI processing result"
                  className="w-full h-full rounded-2xl"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm"
                  onClick={() => handleDownload(processedImage.split('?')[0], 'ai-edited.jpg')}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Wand2 className="w-8 h-8 text-white" />
                </div>
                <p className="text-lg text-stone-600 mb-2">AI magic happens here</p>
                <p className="text-sm text-stone-500">Upload an image to see the transformation</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 编辑控制 */}
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-semibold text-stone-800 mb-3">
            Describe your edit (e.g., "change background to beach sunset", "make it cartoon style")
          </label>
          <div className="relative">
            <input
              type="text"
              placeholder="Remove background and add a professional studio backdrop..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full p-4 pr-12 rounded-xl border border-stone-200 focus:border-amber-500 focus:ring-2 focus:ring-amber-200 transition-all duration-300 text-stone-700 placeholder-stone-400"
            />
            <Wand2 className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-amber-600" />
          </div>
        </div>

        {/* 错误显示 */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-xl flex items-center gap-2 text-red-700">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        {/* 登录提示 */}
        {showLoginPrompt && (
          <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <div className="text-amber-800">
                  <div className="font-semibold">Free trial completed!</div>
                  <div className="text-sm">Sign up to get unlimited AI image editing</div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => router.push('/sign-in')}
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600"
                >
                  Sign Up Free
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowLoginPrompt(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4">
          <Button 
            onClick={handleAIEdit}
            disabled={
              !imageFile || 
              !prompt.trim() || 
              isProcessing || 
              (!user && trialInfo && trialInfo.remaining <= 0)
            }
            className="flex-1 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                AI Magic Processing...
              </>
            ) : !user && trialInfo && trialInfo.remaining <= 0 ? (
              <>
                <Clock className="w-5 h-5 mr-2" />
                Trial Uses Exhausted
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" />
                {!user && trialInfo ? `Generate AI Magic (${trialInfo.remaining} left)` : 'Generate AI Magic'}
              </>
            )}
          </Button>
          {processedImage && (
            <Button 
              variant="outline" 
              className="px-8 py-4 rounded-xl border-stone-300 hover:bg-stone-50 transition-all duration-300"
              onClick={() => handleDownload(processedImage, 'ai-edited.jpg')}
            >
              <Download className="w-5 h-5 mr-2" />
              Download
            </Button>
          )}
        </div>
      </div>

      {/* 快速示例 */}
      <div className="mt-8 pt-8 border-t border-stone-200">
        <p className="text-sm font-semibold text-stone-700 mb-4">Quick examples to try:</p>
        <div className="flex flex-wrap gap-2">
          {[
            "Remove background",
            "Change to cartoon style",
            "Add professional lighting",
            "Make it black and white",
            "Add sunset background",
            "Enhance colors"
          ].map((example, index) => (
            <button
              key={index}
              onClick={() => handleExampleClick(example)}
              className="px-4 py-2 bg-amber-50 hover:bg-amber-100 text-amber-700 rounded-full text-sm border border-amber-200 hover:border-amber-300 transition-all duration-300 cursor-pointer"
            >
              {example}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
