'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, X, AlertCircle } from 'lucide-react';
import { User } from '@/lib/db/schema';

interface ImageUploadProps {
  onImageUpload: (imageUrl: string) => void;
  user: User;
}

export default function ImageUpload({ onImageUpload, user }: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [uploadLimits, setUploadLimits] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get upload limits on component mount
  useEffect(() => {
    fetch('/api/ai/upload')
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          setUploadLimits(data.limits);
        }
      })
      .catch(console.error);
  }, []);

  const validateFile = useCallback((file: File) => {
    if (!uploadLimits) return true;

    // Check file type
    if (!uploadLimits.allowedTypes.includes(file.type)) {
      setError('Invalid file type. Please upload an image file (JPG, PNG, GIF, WebP).');
      return false;
    }

    // Check file size
    if (file.size > uploadLimits.maxFileSize) {
      setError(`File too large. Maximum size for ${uploadLimits.plan} plan is ${uploadLimits.maxFileSizeMB}MB.`);
      return false;
    }

    return true;
  }, [uploadLimits]);

  const uploadFile = useCallback(async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/ai/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      if (data.success) {
        onImageUpload(data.url);
        setUploadProgress(100);
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, [validateFile, onImageUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      uploadFile(files[0]);
    }
  }, [uploadFile]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      uploadFile(files[0]);
    }
  }, [uploadFile]);

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
          ${isUploading ? 'pointer-events-none opacity-50' : 'cursor-pointer hover:border-primary/50'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="space-y-4">
          <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center">
            <Upload className="h-6 w-6 text-muted-foreground" />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">上传图片</h3>
            <p className="text-sm text-muted-foreground">
              拖拽图片到此处，或点击选择文件
            </p>
            {uploadLimits && (
              <p className="text-xs text-muted-foreground">
                最大文件: {uploadLimits.maxFileSizeMB}MB • 支持格式: JPG, PNG, GIF, WebP
              </p>
            )}
          </div>

          <Button variant="outline" disabled={isUploading}>
            {isUploading ? '上传中...' : '选择图片'}
          </Button>
        </div>

        {/* Upload Progress */}
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-lg">
            <div className="w-full max-w-xs space-y-2">
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-sm text-center">上传中... {uploadProgress}%</p>
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="h-auto p-1"
            >
              <X className="h-4 w-4" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Usage Info */}
      {uploadLimits && (
        <div className="text-xs text-muted-foreground space-y-1">
          <p>Current plan: <span className="font-medium capitalize">{uploadLimits.plan}</span></p>
          <p>Upload limit: {uploadLimits.maxFileSizeMB}MB per file</p>
        </div>
      )}
    </div>
  );
}
