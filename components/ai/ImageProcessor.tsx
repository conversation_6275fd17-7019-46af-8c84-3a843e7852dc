'use client';

import { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Wand2, Loader2, AlertCircle, Sparkles } from 'lucide-react';
import { User } from '@/lib/db/schema';

interface ImageProcessorProps {
  imageUrl: string;
  onProcessingStart: () => void;
  onProcessingComplete: (result: string) => void;
  onProcessingError: () => void;
  user: User;
}

const AI_OPERATIONS = [
  {
    id: 'remove-background',
    name: 'Remove Background',
    description: 'Remove the background from your image',
    icon: '🎭',
    cost: '$0.01',
    category: 'Background',
  },
  {
    id: 'replace-background',
    name: 'Replace Background',
    description: 'Replace the background with a new scene',
    icon: '🌅',
    cost: '$0.03',
    category: 'Background',
    requiresInput: true,
    inputLabel: 'New Background',
    inputPlaceholder: 'e.g., beach sunset, office space, mountain landscape',
  },
  {
    id: 'enhance-image',
    name: 'Enhance Quality',
    description: 'Improve image quality and resolution',
    icon: '✨',
    cost: '$0.02',
    category: 'Enhancement',
    options: [
      { value: 'general', label: 'General Enhancement' },
      { value: 'face', label: 'Face Enhancement' },
    ],
  },
  {
    id: 'multi-angle-portrait',
    name: 'Multi-Angle Portrait',
    description: 'Generate different angles of a portrait',
    icon: '👤',
    cost: '$0.04',
    category: 'Portrait',
    options: [
      { value: 'front view', label: 'Front View' },
      { value: 'side profile', label: 'Side Profile' },
      { value: '3/4 view', label: '3/4 View' },
      { value: 'back view', label: 'Back View' },
    ],
  },
  {
    id: 'style-transfer',
    name: 'Style Transfer',
    description: 'Apply artistic styles to your image',
    icon: '🎨',
    cost: '$0.02',
    category: 'Artistic',
    options: [
      { value: 'oil painting', label: 'Oil Painting' },
      { value: 'watercolor', label: 'Watercolor' },
      { value: 'sketch', label: 'Pencil Sketch' },
      { value: 'cartoon', label: 'Cartoon Style' },
      { value: 'vintage', label: 'Vintage Photo' },
    ],
  },
];

export default function ImageProcessor({
  imageUrl,
  onProcessingStart,
  onProcessingComplete,
  onProcessingError,
  user,
}: ImageProcessorProps) {
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [customInput, setCustomInput] = useState<string>('');
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedOp = AI_OPERATIONS.find(op => op.id === selectedOperation);

  const handleProcess = useCallback(async () => {
    if (!selectedOperation) {
      setError('Please select an operation');
      return;
    }

    setIsProcessing(true);
    setError(null);
    onProcessingStart();

    try {
      const parameters: Record<string, any> = {};

      // Add operation-specific parameters
      if (selectedOp?.requiresInput && customInput) {
        if (selectedOperation === 'replace-background') {
          parameters.background = customInput;
        }
      }

      if (selectedOp?.options && selectedOption) {
        if (selectedOperation === 'enhance-image') {
          parameters.type = selectedOption;
        } else if (selectedOperation === 'multi-angle-portrait') {
          parameters.angle = selectedOption;
        } else if (selectedOperation === 'style-transfer') {
          parameters.style = selectedOption;
        }
      }

      const response = await fetch('/api/ai/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          operation: selectedOperation,
          parameters,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Processing failed');
      }

      if (data.success && data.result) {
        onProcessingComplete(data.result);
      } else {
        throw new Error('No result returned');
      }
    } catch (error) {
      console.error('Processing error:', error);
      setError(error instanceof Error ? error.message : 'Processing failed');
      onProcessingError();
    } finally {
      setIsProcessing(false);
    }
  }, [
    selectedOperation,
    customInput,
    selectedOption,
    imageUrl,
    onProcessingStart,
    onProcessingComplete,
    onProcessingError,
    selectedOp,
  ]);

  const resetForm = useCallback(() => {
    setSelectedOperation('');
    setCustomInput('');
    setSelectedOption('');
    setError(null);
  }, []);

  // Group operations by category
  const operationsByCategory = AI_OPERATIONS.reduce((acc, op) => {
    if (!acc[op.category]) {
      acc[op.category] = [];
    }
    acc[op.category].push(op);
    return acc;
  }, {} as Record<string, typeof AI_OPERATIONS>);

  return (
    <div className="space-y-6">
      {/* Operation Selection */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="operation">Select AI Operation</Label>
          <Select value={selectedOperation} onValueChange={setSelectedOperation}>
            <SelectTrigger>
              <SelectValue placeholder="Choose an AI operation..." />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(operationsByCategory).map(([category, operations]) => (
                <div key={category}>
                  <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                    {category}
                  </div>
                  {operations.map((op) => (
                    <SelectItem key={op.id} value={op.id}>
                      <div className="flex items-center gap-2">
                        <span>{op.icon}</span>
                        <span>{op.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {op.cost}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </div>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Operation Details */}
        {selectedOp && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <span>{selectedOp.icon}</span>
                {selectedOp.name}
                <Badge variant="outline">{selectedOp.cost}</Badge>
              </CardTitle>
              <CardDescription>{selectedOp.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Custom Input */}
              {selectedOp.requiresInput && (
                <div>
                  <Label htmlFor="custom-input">{selectedOp.inputLabel}</Label>
                  <Textarea
                    id="custom-input"
                    placeholder={selectedOp.inputPlaceholder}
                    value={customInput}
                    onChange={(e) => setCustomInput(e.target.value)}
                    rows={2}
                  />
                </div>
              )}

              {/* Options */}
              {selectedOp.options && (
                <div>
                  <Label htmlFor="option">Options</Label>
                  <Select value={selectedOption} onValueChange={setSelectedOption}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an option..." />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedOp.options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          onClick={handleProcess}
          disabled={!selectedOperation || isProcessing}
          className="flex-1"
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-4 w-4" />
              Process Image
            </>
          )}
        </Button>
        <Button variant="outline" onClick={resetForm} disabled={isProcessing}>
          Reset
        </Button>
      </div>

      {/* Usage Info */}
      <div className="text-xs text-muted-foreground">
        <p>Current plan: <span className="font-medium capitalize">{user.subscription?.plan || 'free'}</span></p>
        <p>Processing costs are estimates and may vary based on image complexity.</p>
      </div>
    </div>
  );
}
