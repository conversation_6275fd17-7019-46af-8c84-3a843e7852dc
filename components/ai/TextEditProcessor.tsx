'use client';

import { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Wand2, Loader2, AlertCircle, Type, Languages } from 'lucide-react';
import { User } from '@/lib/db/schema';

interface TextEditProcessorProps {
  imageUrl: string;
  onProcessingStart: () => void;
  onProcessingComplete: (result: string) => void;
  onProcessingError: () => void;
  user: User;
}

// 文字编辑功能模板
const TEXT_EDIT_TEMPLATES = [
  {
    id: 'replace-text',
    name: '替换文字',
    description: '将图片中的指定文字替换为新文字',
    cost: '$0.003',
    example: '将"Hello"替换为"你好"'
  },
  {
    id: 'translate-text',
    name: '翻译文字',
    description: '将图片中的文字翻译为其他语言',
    cost: '$0.003',
    example: '将英文翻译为中文'
  },
  {
    id: 'fix-typo',
    name: '修正错误',
    description: '修正图片中的拼写或语法错误',
    cost: '$0.003',
    example: '修正拼写错误'
  },
  {
    id: 'change-style',
    name: '改变样式',
    description: '保持文字内容，改变字体样式',
    cost: '$0.003',
    example: '改为粗体或斜体'
  }
];

const LANGUAGES = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: 'English' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
];

export default function TextEditProcessor({
  imageUrl,
  onProcessingStart,
  onProcessingComplete,
  onProcessingError,
  user,
}: TextEditProcessorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [oldText, setOldText] = useState<string>('');
  const [newText, setNewText] = useState<string>('');
  const [language, setLanguage] = useState<string>('zh');
  const [customInstruction, setCustomInstruction] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedTemp = TEXT_EDIT_TEMPLATES.find(t => t.id === selectedTemplate);

  const handleProcess = useCallback(async () => {
    if (!selectedTemplate) {
      setError('请选择一个文字编辑模板');
      return;
    }

    if (!oldText.trim()) {
      setError('请输入要替换的原文字');
      return;
    }

    if (selectedTemplate === 'replace-text' && !newText.trim()) {
      setError('请输入新文字内容');
      return;
    }

    setIsProcessing(true);
    setError(null);
    onProcessingStart();

    try {
      let parameters: Record<string, any> = {
        oldText: oldText.trim(),
        language: language
      };

      // 根据模板类型设置参数
      switch (selectedTemplate) {
        case 'replace-text':
          parameters.newText = newText.trim();
          break;
        case 'translate-text':
          const targetLang = LANGUAGES.find(l => l.code === language)?.name || '中文';
          parameters.newText = `[翻译为${targetLang}]`;
          parameters.instruction = `将"${oldText}"翻译为${targetLang}`;
          break;
        case 'fix-typo':
          parameters.newText = `[修正后的文字]`;
          parameters.instruction = `修正"${oldText}"中的拼写或语法错误`;
          break;
        case 'change-style':
          parameters.newText = oldText.trim();
          parameters.instruction = customInstruction || '改变文字样式但保持内容不变';
          break;
      }

      const response = await fetch('/api/ai/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          operation: 'text-edit',
          parameters,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '文字编辑失败');
      }

      if (data.success && data.result) {
        onProcessingComplete(data.result);
      } else {
        throw new Error('没有返回处理结果');
      }
    } catch (error) {
      console.error('Text edit error:', error);
      setError(error instanceof Error ? error.message : '文字编辑失败');
      onProcessingError();
    } finally {
      setIsProcessing(false);
    }
  }, [
    selectedTemplate,
    oldText,
    newText,
    language,
    customInstruction,
    imageUrl,
    onProcessingStart,
    onProcessingComplete,
    onProcessingError,
  ]);

  const resetForm = useCallback(() => {
    setSelectedTemplate('');
    setOldText('');
    setNewText('');
    setCustomInstruction('');
    setError(null);
  }, []);

  return (
    <div className="space-y-6">
      {/* 功能介绍 */}
      <Card className="border">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Type className="h-5 w-5" />
            AI文字编辑
            <Badge variant="outline" className="text-xs">
              $0.003
            </Badge>
          </CardTitle>
          <CardDescription>
            智能编辑图片中的文字内容，支持多种语言，保持原有样式和位置。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 模板选择 */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="template">选择编辑类型</Label>
          <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
            <SelectTrigger>
              <SelectValue placeholder="选择文字编辑类型..." />
            </SelectTrigger>
            <SelectContent>
              {TEXT_EDIT_TEMPLATES.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{template.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {template.cost}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 模板详情 */}
        {selectedTemp && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <span>{selectedTemp.name}</span>
                <Badge variant="outline">{selectedTemp.cost}</Badge>
              </CardTitle>
              <CardDescription>{selectedTemp.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 语言选择 */}
              <div>
                <Label htmlFor="language">处理语言</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 原文字输入 */}
              <div>
                <Label htmlFor="old-text">要替换的原文字</Label>
                <Input
                  id="old-text"
                  placeholder="输入图片中要修改的文字..."
                  value={oldText}
                  onChange={(e) => setOldText(e.target.value)}
                />
              </div>

              {/* 新文字输入 */}
              {selectedTemplate === 'replace-text' && (
                <div>
                  <Label htmlFor="new-text">新文字内容</Label>
                  <Input
                    id="new-text"
                    placeholder="输入要替换成的新文字..."
                    value={newText}
                    onChange={(e) => setNewText(e.target.value)}
                  />
                </div>
              )}

              {/* 自定义指令 */}
              {selectedTemplate === 'change-style' && (
                <div>
                  <Label htmlFor="instruction">样式要求</Label>
                  <Textarea
                    id="instruction"
                    placeholder="例如：改为粗体、改为斜体、改为红色等..."
                    value={customInstruction}
                    onChange={(e) => setCustomInstruction(e.target.value)}
                    rows={2}
                  />
                </div>
              )}

              {/* 示例 */}
              <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded">
                <strong>示例：</strong> {selectedTemp.example}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 错误显示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button
          onClick={handleProcess}
          disabled={!selectedTemplate || !oldText.trim() || isProcessing}
          className="flex-1"
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              正在处理文字...
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-4 w-4" />
              开始文字编辑
            </>
          )}
        </Button>
        <Button variant="outline" onClick={resetForm} disabled={isProcessing}>
          重置
        </Button>
      </div>

      {/* 使用提示 */}
      <div className="text-xs text-muted-foreground space-y-1 bg-muted/30 p-3 rounded">
        <p><strong>提示：</strong>文字编辑是我们的核心功能，处理速度快，成本极低</p>
        <p><strong>最佳效果：</strong>图片中的文字清晰可见，背景对比度高</p>
        <p><strong>特色：</strong>支持中文优化，保持原有字体样式和位置</p>
      </div>
    </div>
  );
}
