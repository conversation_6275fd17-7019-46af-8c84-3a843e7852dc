<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="batchGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="batchGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
    <filter id="batchGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#batchGrad1)" opacity="0.1"/>
  
  <!-- Main content -->
  <g transform="translate(40, 30)">
    <!-- Input files grid -->
    <g>
      <text x="60" y="15" text-anchor="middle" font-family="Inter" font-size="12" font-weight="600" fill="white">Input Files</text>
      
      <!-- File grid (3x2) -->
      <g transform="translate(0, 25)">
        <!-- Row 1 -->
        <rect x="0" y="0" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="5" y="5" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="17" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_001</text>
        
        <rect x="42" y="0" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="47" y="5" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="59" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_002</text>
        
        <rect x="84" y="0" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="89" y="5" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="101" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_003</text>
        
        <!-- Row 2 -->
        <rect x="0" y="40" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="5" y="45" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="17" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_004</text>
        
        <rect x="42" y="40" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="47" y="45" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="59" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_005</text>
        
        <rect x="84" y="40" width="35" height="28" rx="4" fill="#374151" stroke="#6B7280" stroke-width="1"/>
        <rect x="89" y="45" width="25" height="18" rx="2" fill="#4B5563"/>
        <text x="101" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="#9CA3AF">IMG_006</text>
      </g>
      
      <text x="60" y="110" text-anchor="middle" font-family="Inter" font-size="9" fill="rgba(255,255,255,0.7)">6 files selected</text>
    </g>
    
    <!-- Processing arrow and controls -->
    <g transform="translate(140, 45)">
      <!-- Arrow -->
      <path d="M0 15 L20 15 M15 10 L20 15 L15 20" stroke="url(#batchGrad1)" stroke-width="3" fill="none"/>
      
      <!-- Processing controls -->
      <g transform="translate(30, -20)">
        <rect x="0" y="0" width="80" height="50" rx="8" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
        
        <text x="40" y="15" text-anchor="middle" font-family="Inter" font-size="10" font-weight="600" fill="white">Batch Settings</text>
        
        <g transform="translate(10, 20)">
          <circle cx="3" cy="3" r="2" fill="#10B981"/>
          <text x="8" y="6" font-family="Inter" font-size="8" fill="white">Enhance</text>
          
          <circle cx="35" cy="3" r="2" fill="#3B82F6"/>
          <text x="40" y="6" font-family="Inter" font-size="8" fill="white">Resize</text>
          
          <circle cx="3" cy="13" r="2" fill="#8B5CF6"/>
          <text x="8" y="16" font-family="Inter" font-size="8" fill="white">Format</text>
          
          <circle cx="35" cy="13" r="2" fill="#EC4899"/>
          <text x="40" y="16" font-family="Inter" font-size="8" fill="white">Compress</text>
        </g>
      </g>
      
      <!-- AI Processing indicator -->
      <g transform="translate(55, 35)">
        <circle cx="15" cy="15" r="12" fill="url(#batchGrad1)" filter="url(#batchGlow)"/>
        <rect x="10" y="10" width="10" height="10" rx="2" fill="white"/>
        <rect x="12" y="12" width="6" height="6" rx="1" fill="url(#batchGrad1)"/>
        <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="9" font-weight="600" fill="url(#batchGrad1)">AI Batch</text>
      </g>
    </g>
    
    <!-- Output files -->
    <g transform="translate(280, 0)">
      <text x="60" y="15" text-anchor="middle" font-family="Inter" font-size="12" font-weight="600" fill="white">Processed Files</text>
      
      <!-- Processed file grid -->
      <g transform="translate(0, 25)">
        <!-- Row 1 -->
        <rect x="0" y="0" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="5" y="5" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="12" cy="10" r="1.5" fill="white"/>
        <text x="17" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_001</text>
        
        <rect x="42" y="0" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="47" y="5" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="54" cy="10" r="1.5" fill="white"/>
        <text x="59" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_002</text>
        
        <rect x="84" y="0" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="89" y="5" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="96" cy="10" r="1.5" fill="white"/>
        <text x="101" y="32" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_003</text>
        
        <!-- Row 2 -->
        <rect x="0" y="40" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="5" y="45" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="12" cy="50" r="1.5" fill="white"/>
        <text x="17" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_004</text>
        
        <rect x="42" y="40" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="47" y="45" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="54" cy="50" r="1.5" fill="white"/>
        <text x="59" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_005</text>
        
        <rect x="84" y="40" width="35" height="28" rx="4" fill="url(#batchGrad2)" stroke="white" stroke-width="1"/>
        <rect x="89" y="45" width="25" height="18" rx="2" fill="rgba(255,255,255,0.2)"/>
        <circle cx="96" cy="50" r="1.5" fill="white"/>
        <text x="101" y="72" text-anchor="middle" font-family="Inter" font-size="7" fill="white">IMG_006</text>
      </g>
      
      <text x="60" y="110" text-anchor="middle" font-family="Inter" font-size="9" fill="#10B981">6 files processed</text>
    </g>
    
    <!-- Feature description -->
    <g transform="translate(0, 140)">
      <rect x="0" y="0" width="360" height="100" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      
      <g transform="translate(20, 20)">
        <text x="0" y="15" font-family="Inter" font-size="14" font-weight="600" fill="white">Batch Processing</text>
        <text x="0" y="30" font-family="Inter" font-size="11" fill="rgba(255,255,255,0.7)">Process hundreds of images simultaneously with consistent results</text>
        
        <g transform="translate(0, 45)">
          <circle cx="5" cy="5" r="3" fill="#06B6D4"/>
          <text x="15" y="9" font-family="Inter" font-size="11" fill="white">Bulk operations</text>
          
          <circle cx="5" cy="20" r="3" fill="#10B981"/>
          <text x="15" y="24" font-family="Inter" font-size="11" fill="white">Queue management</text>
          
          <circle cx="150" cy="5" r="3" fill="#3B82F6"/>
          <text x="160" y="9" font-family="Inter" font-size="11" fill="white">Progress tracking</text>
          
          <circle cx="150" cy="20" r="3" fill="#8B5CF6"/>
          <text x="160" y="24" font-family="Inter" font-size="11" fill="white">Auto-download</text>
        </g>
      </g>
    </g>
  </g>
</svg>
