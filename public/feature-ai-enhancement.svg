<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="enhanceGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="enhanceGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#enhanceGrad1)" opacity="0.1"/>
  
  <!-- Before/After comparison -->
  <g transform="translate(50, 50)">
    <!-- Before image (left) -->
    <rect x="0" y="0" width="140" height="100" rx="8" fill="#374151" stroke="#6B7280" stroke-width="2"/>
    <text x="70" y="30" text-anchor="middle" font-family="Inter" font-size="12" fill="#9CA3AF">Before</text>
    <rect x="20" y="40" width="100" height="40" rx="4" fill="#4B5563" opacity="0.6"/>
    <circle cx="40" cy="55" r="3" fill="#6B7280"/>
    
    <!-- AI Enhancement Arrow -->
    <g transform="translate(160, 40)">
      <circle cx="15" cy="15" r="15" fill="url(#enhanceGrad1)" filter="url(#glow)"/>
      <path d="M8 15 L15 8 L22 15 L15 22 Z" fill="white"/>
      <text x="15" y="40" text-anchor="middle" font-family="Inter" font-size="10" font-weight="600" fill="url(#enhanceGrad1)">AI Enhance</text>
    </g>
    
    <!-- After image (right) -->
    <rect x="210" y="0" width="140" height="100" rx="8" fill="url(#enhanceGrad2)" stroke="url(#enhanceGrad1)" stroke-width="2"/>
    <text x="280" y="30" text-anchor="middle" font-family="Inter" font-size="12" fill="white">After</text>
    <rect x="230" y="40" width="100" height="40" rx="4" fill="white" opacity="0.9"/>
    <circle cx="250" cy="55" r="3" fill="url(#enhanceGrad1)"/>
    
    <!-- Enhancement indicators -->
    <g transform="translate(0, 120)">
      <rect x="0" y="0" width="300" height="80" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      
      <!-- Feature list -->
      <g transform="translate(20, 20)">
        <circle cx="5" cy="5" r="3" fill="#10B981"/>
        <text x="15" y="9" font-family="Inter" font-size="12" fill="white">Smart Color Enhancement</text>
        
        <circle cx="5" cy="25" r="3" fill="#3B82F6"/>
        <text x="15" y="29" font-family="Inter" font-size="12" fill="white">Automatic Noise Reduction</text>
        
        <circle cx="150" cy="5" r="3" fill="#8B5CF6"/>
        <text x="160" y="9" font-family="Inter" font-size="12" fill="white">Intelligent Sharpening</text>
        
        <circle cx="150" cy="25" r="3" fill="#EC4899"/>
        <text x="160" y="29" font-family="Inter" font-size="12" fill="white">HDR Processing</text>
      </g>
    </g>
  </g>
</svg>
