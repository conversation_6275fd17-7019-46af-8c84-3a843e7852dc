<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="objGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="objGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EF4444;stop-opacity:1" />
    </linearGradient>
    <filter id="objGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#objGrad1)" opacity="0.1"/>
  
  <!-- Main demonstration -->
  <g transform="translate(50, 40)">
    <!-- Before image -->
    <g>
      <rect x="0" y="0" width="130" height="100" rx="8" fill="#1F2937" stroke="#374151" stroke-width="2"/>
      <text x="65" y="20" text-anchor="middle" font-family="Inter" font-size="11" fill="#9CA3AF">Before</text>
      
      <!-- Scene background -->
      <rect x="10" y="25" width="110" height="65" rx="4" fill="#4B5563"/>
      
      <!-- Objects to be removed -->
      <circle cx="40" cy="50" r="8" fill="url(#objGrad2)" opacity="0.8" stroke="#EF4444" stroke-width="2" stroke-dasharray="3,3"/>
      <rect x="70" y="40" width="15" height="20" rx="2" fill="url(#objGrad2)" opacity="0.8" stroke="#EF4444" stroke-width="2" stroke-dasharray="3,3"/>
      <ellipse cx="100" cy="65" rx="10" ry="6" fill="url(#objGrad2)" opacity="0.8" stroke="#EF4444" stroke-width="2" stroke-dasharray="3,3"/>
      
      <!-- Selection indicators -->
      <text x="65" y="95" text-anchor="middle" font-family="Inter" font-size="8" fill="#EF4444">Objects Selected</text>
    </g>
    
    <!-- AI Processing -->
    <g transform="translate(150, 45)">
      <circle cx="15" cy="15" r="12" fill="url(#objGrad1)" filter="url(#objGlow)"/>
      <path d="M8 15 L15 8 L22 15 L15 22 Z" fill="white"/>
      <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="9" font-weight="600" fill="url(#objGrad1)">AI Remove</text>
      
      <!-- Magic sparkles -->
      <g opacity="0.8">
        <path d="M5 5 L7 10 L12 8 L7 13 L5 5" fill="#F59E0B">
          <animateTransform attributeName="transform" type="rotate" values="0 5 5;360 5 5" dur="2s" repeatCount="indefinite"/>
        </path>
        <path d="M25 20 L27 25 L32 23 L27 28 L25 20" fill="#10B981">
          <animateTransform attributeName="transform" type="rotate" values="0 25 20;-360 25 20" dur="3s" repeatCount="indefinite"/>
        </path>
      </g>
    </g>
    
    <!-- After image -->
    <g transform="translate(190, 0)">
      <rect x="0" y="0" width="130" height="100" rx="8" fill="#1F2937" stroke="url(#objGrad1)" stroke-width="2"/>
      <text x="65" y="20" text-anchor="middle" font-family="Inter" font-size="11" fill="white">After</text>
      
      <!-- Clean scene background -->
      <rect x="10" y="25" width="110" height="65" rx="4" fill="#4B5563"/>
      
      <!-- Seamlessly filled areas where objects were -->
      <circle cx="40" cy="50" r="8" fill="#4B5563" opacity="0.9"/>
      <rect x="70" y="40" width="15" height="20" rx="2" fill="#4B5563" opacity="0.9"/>
      <ellipse cx="100" cy="65" rx="10" ry="6" fill="#4B5563" opacity="0.9"/>
      
      <text x="65" y="95" text-anchor="middle" font-family="Inter" font-size="8" fill="#10B981">Objects Removed</text>
    </g>
    
    <!-- Feature description -->
    <g transform="translate(0, 120)">
      <rect x="0" y="0" width="320" height="100" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      
      <g transform="translate(20, 20)">
        <text x="0" y="15" font-family="Inter" font-size="14" font-weight="600" fill="white">Smart Object Removal</text>
        
        <g transform="translate(0, 30)">
          <circle cx="5" cy="5" r="3" fill="#10B981"/>
          <text x="15" y="9" font-family="Inter" font-size="11" fill="white">Content-aware fill</text>
          
          <circle cx="5" cy="20" r="3" fill="#06B6D4"/>
          <text x="15" y="24" font-family="Inter" font-size="11" fill="white">Seamless blending</text>
          
          <circle cx="150" cy="5" r="3" fill="#F59E0B"/>
          <text x="160" y="9" font-family="Inter" font-size="11" fill="white">Multiple selections</text>
          
          <circle cx="150" cy="20" r="3" fill="#EF4444"/>
          <text x="160" y="24" font-family="Inter" font-size="11" fill="white">Instant processing</text>
        </g>
      </g>
    </g>
  </g>
</svg>
