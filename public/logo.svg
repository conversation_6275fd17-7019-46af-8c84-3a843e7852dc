<svg width="200" height="48" viewBox="0 0 200 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#EA580C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="logoGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo Icon -->
  <g transform="translate(4, 4)">
    <!-- Background circle -->
    <circle cx="20" cy="20" r="18" fill="url(#logoGrad1)" stroke="white" stroke-width="2"/>
    
    <!-- AI Neural network -->
    <g transform="translate(10, 10)">
      <!-- Central node -->
      <circle cx="10" cy="10" r="2.5" fill="white" opacity="0.9"/>
      
      <!-- Connection lines -->
      <line x1="10" y1="10" x2="5" y2="5" stroke="white" stroke-width="2" opacity="0.7"/>
      <line x1="10" y1="10" x2="15" y2="5" stroke="white" stroke-width="2" opacity="0.7"/>
      <line x1="10" y1="10" x2="5" y2="15" stroke="white" stroke-width="2" opacity="0.7"/>
      <line x1="10" y1="10" x2="15" y2="15" stroke="white" stroke-width="2" opacity="0.7"/>
      <line x1="10" y1="10" x2="10" y2="3" stroke="white" stroke-width="2" opacity="0.7"/>
      <line x1="10" y1="10" x2="10" y2="17" stroke="white" stroke-width="2" opacity="0.7"/>
      
      <!-- Outer nodes -->
      <circle cx="5" cy="5" r="2" fill="white" opacity="0.8"/>
      <circle cx="15" cy="5" r="2" fill="white" opacity="0.8"/>
      <circle cx="5" cy="15" r="2" fill="white" opacity="0.8"/>
      <circle cx="15" cy="15" r="2" fill="white" opacity="0.8"/>
      <circle cx="10" cy="3" r="2" fill="white" opacity="0.8"/>
      <circle cx="10" cy="17" r="2" fill="white" opacity="0.8"/>
    </g>
    
    <!-- Image/Edit indicator -->
    <rect x="26" y="26" width="10" height="8" rx="1.5" fill="url(#logoGrad2)" opacity="0.9"/>
    <rect x="27" y="27" width="8" height="6" rx="1" fill="white" opacity="0.3"/>
    <circle cx="29" cy="29" r="1" fill="white"/>
  </g>
  
  <!-- Text Logo -->
  <g transform="translate(52, 12)">
    <!-- AI -->
    <text x="0" y="20" font-family="Inter, system-ui, sans-serif" font-size="24" font-weight="800" fill="url(#logoGrad1)">AI</text>

    <!-- Image Editor -->
    <text x="32" y="20" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="#57534E">Image</text>
    <text x="90" y="20" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="url(#logoGrad2)">Editor</text>
  </g>
</svg>
