<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="styleGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EC4899;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="styleGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EF4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="styleGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <filter id="styleGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#styleGrad1)" opacity="0.1"/>
  
  <!-- Main content -->
  <g transform="translate(30, 30)">
    <!-- Original image -->
    <g>
      <rect x="0" y="0" width="100" height="80" rx="8" fill="#374151" stroke="#6B7280" stroke-width="2"/>
      <text x="50" y="15" text-anchor="middle" font-family="Inter" font-size="10" fill="#9CA3AF">Original</text>
      
      <!-- Simple content representation -->
      <rect x="15" y="25" width="70" height="45" rx="4" fill="#4B5563"/>
      <circle cx="35" cy="40" r="8" fill="#6B7280"/>
      <rect x="55" y="35" width="20" height="15" rx="2" fill="#6B7280"/>
    </g>
    
    <!-- Style options -->
    <g transform="translate(120, 0)">
      <text x="50" y="15" text-anchor="middle" font-family="Inter" font-size="11" font-weight="600" fill="white">Choose Style</text>
      
      <!-- Style 1: Artistic -->
      <g transform="translate(0, 20)">
        <rect x="0" y="0" width="30" height="25" rx="4" fill="url(#styleGrad1)" stroke="white" stroke-width="1"/>
        <circle cx="15" cy="12" r="3" fill="white" opacity="0.8"/>
        <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="8" fill="white">Artistic</text>
      </g>
      
      <!-- Style 2: Vintage -->
      <g transform="translate(35, 20)">
        <rect x="0" y="0" width="30" height="25" rx="4" fill="url(#styleGrad2)" stroke="white" stroke-width="1"/>
        <rect x="5" y="5" width="20" height="15" rx="2" fill="white" opacity="0.6"/>
        <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="8" fill="white">Vintage</text>
      </g>
      
      <!-- Style 3: Modern -->
      <g transform="translate(70, 20)">
        <rect x="0" y="0" width="30" height="25" rx="4" fill="url(#styleGrad3)" stroke="white" stroke-width="1"/>
        <path d="M10 8 L20 8 L25 15 L5 15 Z" fill="white" opacity="0.8"/>
        <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="8" fill="white">Modern</text>
      </g>
    </g>
    
    <!-- AI Processing -->
    <g transform="translate(250, 35)">
      <circle cx="15" cy="15" r="12" fill="url(#styleGrad1)" filter="url(#styleGlow)"/>
      <path d="M8 12 L12 8 L16 12 L20 8 L16 16 L12 20 L8 16 L12 12 Z" fill="white"/>
      <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="9" font-weight="600" fill="url(#styleGrad1)">AI Style</text>
    </g>
    
    <!-- Result -->
    <g transform="translate(290, 0)">
      <rect x="0" y="0" width="100" height="80" rx="8" fill="url(#styleGrad1)" stroke="white" stroke-width="2"/>
      <text x="50" y="15" text-anchor="middle" font-family="Inter" font-size="10" fill="white">Stylized</text>
      
      <!-- Stylized content -->
      <rect x="15" y="25" width="70" height="45" rx="4" fill="rgba(255,255,255,0.2)"/>
      <circle cx="35" cy="40" r="8" fill="rgba(255,255,255,0.8)"/>
      <rect x="55" y="35" width="20" height="15" rx="2" fill="rgba(255,255,255,0.6)"/>
      
      <!-- Style effect overlay -->
      <g opacity="0.6">
        <circle cx="25" cy="35" r="2" fill="#EC4899"/>
        <circle cx="65" cy="45" r="2" fill="#8B5CF6"/>
        <circle cx="45" cy="55" r="2" fill="#EC4899"/>
      </g>
    </g>
    
    <!-- Feature details -->
    <g transform="translate(0, 100)">
      <rect x="0" y="0" width="340" height="110" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      
      <g transform="translate(20, 20)">
        <text x="0" y="15" font-family="Inter" font-size="14" font-weight="600" fill="white">AI Style Transfer</text>
        <text x="0" y="30" font-family="Inter" font-size="11" fill="rgba(255,255,255,0.7)">Transform your images with artistic styles powered by neural networks</text>
        
        <g transform="translate(0, 45)">
          <circle cx="5" cy="5" r="3" fill="#8B5CF6"/>
          <text x="15" y="9" font-family="Inter" font-size="11" fill="white">20+ artistic styles</text>
          
          <circle cx="5" cy="20" r="3" fill="#EC4899"/>
          <text x="15" y="24" font-family="Inter" font-size="11" fill="white">Custom style upload</text>
          
          <circle cx="170" cy="5" r="3" fill="#F59E0B"/>
          <text x="180" y="9" font-family="Inter" font-size="11" fill="white">Intensity control</text>
          
          <circle cx="170" cy="20" r="3" fill="#06B6D4"/>
          <text x="180" y="24" font-family="Inter" font-size="11" fill="white">High-res output</text>
        </g>
      </g>
    </g>
  </g>
</svg>
