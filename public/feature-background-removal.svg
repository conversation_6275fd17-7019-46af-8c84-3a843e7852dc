<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EC4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bgGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <pattern id="checkerboard" patternUnits="userSpaceOnUse" width="10" height="10">
      <rect width="5" height="5" fill="#E5E7EB"/>
      <rect x="5" y="5" width="5" height="5" fill="#E5E7EB"/>
      <rect x="5" y="0" width="5" height="5" fill="#F3F4F6"/>
      <rect x="0" y="5" width="5" height="5" fill="#F3F4F6"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGrad1)" opacity="0.1"/>
  
  <!-- Main content -->
  <g transform="translate(50, 40)">
    <!-- Original image with background -->
    <g>
      <rect x="0" y="0" width="120" height="90" rx="8" fill="#4B5563" stroke="#6B7280" stroke-width="2"/>
      <text x="60" y="20" text-anchor="middle" font-family="Inter" font-size="11" fill="#9CA3AF">Original</text>
      
      <!-- Background elements -->
      <rect x="10" y="25" width="100" height="55" rx="4" fill="#6B7280" opacity="0.4"/>
      <circle cx="30" cy="40" r="8" fill="#9CA3AF" opacity="0.6"/>
      <rect x="70" y="35" width="30" height="20" rx="2" fill="#9CA3AF" opacity="0.6"/>
      
      <!-- Subject (person/object) -->
      <ellipse cx="60" cy="55" rx="15" ry="20" fill="url(#bgGrad1)"/>
    </g>
    
    <!-- AI Processing indicator -->
    <g transform="translate(140, 35)">
      <circle cx="15" cy="15" r="12" fill="url(#bgGrad1)" opacity="0.9"/>
      <path d="M10 15 L15 10 L20 15 L15 20 Z" fill="white"/>
      <text x="15" y="35" text-anchor="middle" font-family="Inter" font-size="9" font-weight="600" fill="url(#bgGrad1)">AI Remove</text>
      
      <!-- Processing dots -->
      <circle cx="5" cy="0" r="2" fill="url(#bgGrad1)" opacity="0.6">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="-5" r="2" fill="url(#bgGrad1)" opacity="0.6">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" begin="0.3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="25" cy="0" r="2" fill="url(#bgGrad1)" opacity="0.6">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" begin="0.6s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- Result with transparent background -->
    <g transform="translate(180, 0)">
      <rect x="0" y="0" width="120" height="90" rx="8" fill="url(#checkerboard)" stroke="url(#bgGrad1)" stroke-width="2"/>
      <text x="60" y="20" text-anchor="middle" font-family="Inter" font-size="11" fill="url(#bgGrad1)">Background Removed</text>
      
      <!-- Only the subject remains -->
      <ellipse cx="60" cy="55" rx="15" ry="20" fill="url(#bgGrad1)"/>
      
      <!-- Transparency indicator -->
      <text x="60" y="75" text-anchor="middle" font-family="Inter" font-size="8" fill="#6B7280">Transparent</text>
    </g>
    
    <!-- Feature highlights -->
    <g transform="translate(0, 110)">
      <rect x="0" y="0" width="300" height="100" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      
      <g transform="translate(20, 20)">
        <text x="0" y="15" font-family="Inter" font-size="14" font-weight="600" fill="white">One-Click Background Removal</text>
        
        <g transform="translate(0, 30)">
          <circle cx="5" cy="5" r="3" fill="#EC4899"/>
          <text x="15" y="9" font-family="Inter" font-size="11" fill="white">AI-powered precision</text>
          
          <circle cx="5" cy="20" r="3" fill="#8B5CF6"/>
          <text x="15" y="24" font-family="Inter" font-size="11" fill="white">Hair & edge detection</text>
          
          <circle cx="150" cy="5" r="3" fill="#06B6D4"/>
          <text x="160" y="9" font-family="Inter" font-size="11" fill="white">Batch processing</text>
          
          <circle cx="150" cy="20" r="3" fill="#3B82F6"/>
          <text x="160" y="24" font-family="Inter" font-size="11" fill="white">Multiple formats</text>
        </g>
      </g>
    </g>
  </g>
</svg>
