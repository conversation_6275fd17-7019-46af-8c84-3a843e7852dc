<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#EA580C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="white" stroke-width="2"/>
  
  <!-- AI Brain/Neural network pattern -->
  <g transform="translate(8, 8)">
    <!-- Central node -->
    <circle cx="8" cy="8" r="2" fill="white" opacity="0.9"/>
    
    <!-- Connection lines -->
    <line x1="8" y1="8" x2="4" y2="4" stroke="white" stroke-width="1.5" opacity="0.7"/>
    <line x1="8" y1="8" x2="12" y2="4" stroke="white" stroke-width="1.5" opacity="0.7"/>
    <line x1="8" y1="8" x2="4" y2="12" stroke="white" stroke-width="1.5" opacity="0.7"/>
    <line x1="8" y1="8" x2="12" y2="12" stroke="white" stroke-width="1.5" opacity="0.7"/>
    <line x1="8" y1="8" x2="8" y2="2" stroke="white" stroke-width="1.5" opacity="0.7"/>
    <line x1="8" y1="8" x2="8" y2="14" stroke="white" stroke-width="1.5" opacity="0.7"/>
    
    <!-- Outer nodes -->
    <circle cx="4" cy="4" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="12" cy="4" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="4" cy="12" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="12" cy="12" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="8" cy="2" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="8" cy="14" r="1.5" fill="white" opacity="0.8"/>
  </g>
  
  <!-- Image/Edit indicator -->
  <rect x="20" y="20" width="8" height="6" rx="1" fill="url(#grad2)" opacity="0.9"/>
  <rect x="21" y="21" width="6" height="4" rx="0.5" fill="white" opacity="0.3"/>
  <circle cx="22.5" cy="22.5" r="0.5" fill="white"/>
</svg>
