import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

dotenv.config();

const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

async function createTrialTable() {
  try {
    console.log('Creating trial_usage table...');
    
    await client`
      CREATE TABLE IF NOT EXISTS trial_usage (
        id SERIAL PRIMARY KEY,
        fingerprint VARCHAR(255) NOT NULL,
        date VARCHAR(10) NOT NULL,
        operation_type VARCHAR(50) NOT NULL,
        prompt TEXT,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `;
    
    console.log('✅ trial_usage table created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating trial_usage table:', error);
    process.exit(1);
  }
}

createTrialTable();