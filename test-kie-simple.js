// 简化的kie.ai API测试
const KIE_API_KEY = "4205bbf5a92b68ed52e37c735c6a8bb6";

async function testKieSimple() {
  console.log('🔍 Testing kie.ai API with different approaches...\n');
  
  // 测试不同的基础URL和路径
  const baseUrls = [
    'https://api.kie.ai',
    'https://kie.ai/api',
    'https://inference.kie.ai',
  ];
  
  const paths = [
    '/v1/predictions',
    '/predictions', 
    '/models/google/nano-banana',
    '/run',
    '/generate',
    '/inference'
  ];
  
  const testPayload = {
    model: "google/nano-banana",
    input: {
      prompt: "test prompt",
      image: "https://example.com/test.jpg"
    }
  };
  
  for (const baseUrl of baseUrls) {
    for (const path of paths) {
      const fullUrl = `${baseUrl}${path}`;
      
      try {
        console.log(`Testing: ${fullUrl}`);
        
        const response = await fetch(fullUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${KIE_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testPayload),
        });
        
        console.log(`  Status: ${response.status}`);
        
        if (response.status === 200) {
          const data = await response.json();
          console.log(`  ✅ SUCCESS! Response:`, data);
          return;
        } else if (response.status === 401) {
          console.log(`  🔑 Authentication issue - check API key`);
        } else if (response.status === 400) {
          const error = await response.text();
          console.log(`  📝 Bad request - possible payload issue:`, error.substring(0, 100));
        } else if (response.status !== 404) {
          const error = await response.text();
          console.log(`  ❓ Unexpected status:`, error.substring(0, 100));
        }
        
      } catch (error) {
        console.log(`  ❌ Network error: ${error.message}`);
      }
    }
  }
  
  console.log('\n💡 All endpoints returned 404 or failed');
  console.log('This suggests kie.ai might use a different API structure or require account setup');
}

testKieSimple();