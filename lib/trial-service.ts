import { db } from '@/lib/db/drizzle';
import { trialUsage } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import crypto from 'crypto';

export interface TrialLimits {
  dailyLimit: number;
  maxFileSize: number;
  allowedOperations: string[];
}

export interface TrialUsageResult {
  allowed: boolean;
  remaining: number;
  resetTime?: string;
}

// Trial limit configuration
export const TRIAL_LIMITS: TrialLimits = {
  dailyLimit: 3,
  maxFileSize: 20 * 1024 * 1024, // 20MB
  allowedOperations: ['background-replace', 'text-edit', 'enhance-face', 'portrait-generation']
};

// Generate consistent fingerprint for trial users
export function generateTrialFingerprint(request: Request): string {
  const userAgent = request.headers.get('user-agent') || '';
  const acceptLanguage = request.headers.get('accept-language') || '';
  const forwarded = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown';
  
  const rawFingerprint = `${userAgent}-${acceptLanguage}-${forwarded}`;
  
  // Create a hash for privacy and consistency
  return crypto
    .createHash('sha256')
    .update(rawFingerprint)
    .digest('hex')
    .substring(0, 32);
}

// Check trial usage for the day
export async function checkTrialUsage(fingerprint: string): Promise<TrialUsageResult> {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  
  try {
    // Get today's usage count for this fingerprint
    const usage = await db
      .select()
      .from(trialUsage)
      .where(
        and(
          eq(trialUsage.fingerprint, fingerprint),
          eq(trialUsage.date, today)
        )
      );

    const usedCount = usage.length;
    const remaining = Math.max(0, TRIAL_LIMITS.dailyLimit - usedCount);

    if (usedCount >= TRIAL_LIMITS.dailyLimit) {
      // Calculate reset time (tomorrow at 00:00)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      return { 
        allowed: false, 
        remaining: 0, 
        resetTime: tomorrow.toISOString()
      };
    }

    return { 
      allowed: true, 
      remaining
    };

  } catch (error) {
    console.error('Error checking trial usage:', error);
    // On error, be conservative and allow usage
    return { allowed: true, remaining: TRIAL_LIMITS.dailyLimit };
  }
}

// Record trial usage
export async function recordTrialUsage(
  fingerprint: string, 
  operationType: string, 
  prompt?: string
): Promise<void> {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  
  try {
    await db.insert(trialUsage).values({
      fingerprint,
      date: today,
      operationType,
      prompt: prompt || null,
      createdAt: new Date()
    });
  } catch (error) {
    console.error('Error recording trial usage:', error);
    // Non-critical error, don't throw
  }
}

// Get trial usage statistics
export async function getTrialStats(fingerprint: string): Promise<{
  dailyUsed: number;
  dailyLimit: number;
  remaining: number;
  lastUsed?: Date;
}> {
  const today = new Date().toISOString().split('T')[0];
  
  try {
    const usage = await db
      .select()
      .from(trialUsage)
      .where(
        and(
          eq(trialUsage.fingerprint, fingerprint),
          eq(trialUsage.date, today)
        )
      );

    const dailyUsed = usage.length;
    const remaining = Math.max(0, TRIAL_LIMITS.dailyLimit - dailyUsed);
    const lastUsed = usage.length > 0 ? usage[usage.length - 1].createdAt : undefined;

    return {
      dailyUsed,
      dailyLimit: TRIAL_LIMITS.dailyLimit,
      remaining,
      lastUsed
    };

  } catch (error) {
    console.error('Error getting trial stats:', error);
    return {
      dailyUsed: 0,
      dailyLimit: TRIAL_LIMITS.dailyLimit,
      remaining: TRIAL_LIMITS.dailyLimit
    };
  }
}