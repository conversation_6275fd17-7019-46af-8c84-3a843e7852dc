-- Migration: Add AI Image Processing Tables
-- Created: 2024-01-20

-- Create image_processes table
CREATE TABLE IF NOT EXISTS "image_processes" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"team_id" integer,
	"original_url" text NOT NULL,
	"processed_url" text,
	"prompt" text NOT NULL,
	"status" varchar(20) DEFAULT 'processing' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Create usage_stats table
CREATE TABLE IF NOT EXISTS "usage_stats" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"team_id" integer,
	"feature_type" varchar(50) NOT NULL,
	"usage_count" integer DEFAULT 1 NOT NULL,
	"date" date DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "image_processes" ADD CONSTRAINT "image_processes_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "image_processes" ADD CONSTRAINT "image_processes_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "teams"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "usage_stats" ADD CONSTRAINT "usage_stats_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "usage_stats" ADD CONSTRAINT "usage_stats_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "teams"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_image_processes_user_id" ON "image_processes" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_image_processes_team_id" ON "image_processes" ("team_id");
CREATE INDEX IF NOT EXISTS "idx_image_processes_status" ON "image_processes" ("status");
CREATE INDEX IF NOT EXISTS "idx_image_processes_created_at" ON "image_processes" ("created_at");

CREATE INDEX IF NOT EXISTS "idx_usage_stats_user_id" ON "usage_stats" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_usage_stats_team_id" ON "usage_stats" ("team_id");
CREATE INDEX IF NOT EXISTS "idx_usage_stats_feature_type" ON "usage_stats" ("feature_type");
CREATE INDEX IF NOT EXISTS "idx_usage_stats_date" ON "usage_stats" ("date");

-- Create unique constraint for usage_stats to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS "idx_usage_stats_unique" ON "usage_stats" ("user_id", "feature_type", "date") WHERE "team_id" IS NULL;
CREATE UNIQUE INDEX IF NOT EXISTS "idx_usage_stats_team_unique" ON "usage_stats" ("user_id", "team_id", "feature_type", "date") WHERE "team_id" IS NOT NULL;
