import Replicate from 'replicate';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// AI Models Configuration - 统一使用qwen-image-edit模型
export const AI_MODELS = {
  // 所有功能都使用qwen-image-edit模型
  QWEN_IMAGE_EDIT: "qwen/qwen-image-edit",

  // 保留旧的常量名以兼容现有代码，但都指向qwen-image-edit
  BACKGROUND_REMOVAL: "qwen/qwen-image-edit",
  BACKGROUND_REPLACE: "qwen/qwen-image-edit",
  REAL_ESRGAN: "qwen/qwen-image-edit",
  GFPGAN: "qwen/qwen-image-edit",
  INSTANT_ID: "qwen/qwen-image-edit",
  FACE_SWAP: "qwen/qwen-image-edit",
  STYLE_TRANSFER: "qwen/qwen-image-edit",
  ARTISTIC_STYLE: "qwen/qwen-image-edit",
};

// Image processing functions - 统一使用qwen-image-edit模型
export async function removeBackground(imageUrl: string) {
  try {
    const output = await replicate.run(AI_MODELS.QWEN_IMAGE_EDIT, {
      input: {
        image: imageUrl,
        prompt: "Remove the background from this image, keep the main subject intact",
        num_inference_steps: 20,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      }
    });
    return { success: true, result: output };
  } catch (error) {
    console.error('Background removal failed:', error);
    return { success: false, error: error.message };
  }
}

export async function replaceBackground(imageUrl: string, newBackground: string) {
  try {
    const output = await replicate.run(AI_MODELS.QWEN_IMAGE_EDIT, {
      input: {
        image: imageUrl,
        prompt: `Replace the background with: ${newBackground}, professional photography, high quality, detailed`,
        num_inference_steps: 20,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      }
    });
    return { success: true, result: output };
  } catch (error) {
    console.error('Background replacement failed:', error);
    return { success: false, error: error.message };
  }
}

export async function enhanceImage(imageUrl: string, enhancementType: 'face' | 'general' = 'general') {
  try {
    const prompt = enhancementType === 'face'
      ? "Enhance and beautify the face in this image, improve skin texture, remove blemishes, enhance facial features naturally"
      : "Enhance the overall quality of this image, improve clarity, colors, and details";

    const output = await replicate.run(AI_MODELS.QWEN_IMAGE_EDIT, {
      input: {
        image: imageUrl,
        prompt: prompt,
        num_inference_steps: 20,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      }
    });
    return { success: true, result: output };
  } catch (error) {
    console.error('Image enhancement failed:', error);
    return { success: false, error: error.message };
  }
}

export async function generateMultiAnglePortrait(imageUrl: string, angle: string) {
  try {
    const output = await replicate.run(AI_MODELS.QWEN_IMAGE_EDIT, {
      input: {
        image: imageUrl,
        prompt: `Generate a professional headshot portrait, ${angle} angle, studio lighting, high quality portrait photography`,
        num_inference_steps: 20,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      }
    });
    return { success: true, result: output };
  } catch (error) {
    console.error('Multi-angle portrait generation failed:', error);
    return { success: false, error: error.message };
  }
}

export async function applyStyleTransfer(imageUrl: string, style: string) {
  try {
    const output = await replicate.run(AI_MODELS.QWEN_IMAGE_EDIT, {
      input: {
        image: imageUrl,
        prompt: `Apply ${style} style to this image, artistic transformation, high quality`,
        num_inference_steps: 20,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      }
    });
    return { success: true, result: output };
  } catch (error) {
    console.error('Style transfer failed:', error);
    return { success: false, error: error.message };
  }
}

// Generic image processing function
export async function processImage(
  imageUrl: string,
  operation: string,
  parameters: Record<string, any> = {}
) {
  try {
    switch (operation) {
      case 'remove-background':
        return await removeBackground(imageUrl);
      
      case 'replace-background':
        return await replaceBackground(imageUrl, parameters.background || 'white background');
      
      case 'enhance-image':
        return await enhanceImage(imageUrl, parameters.type || 'general');
      
      case 'multi-angle-portrait':
        return await generateMultiAnglePortrait(imageUrl, parameters.angle || 'front view');
      
      case 'style-transfer':
        return await applyStyleTransfer(imageUrl, parameters.style || 'oil painting');
      
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  } catch (error) {
    console.error(`Image processing failed for operation ${operation}:`, error);
    return { success: false, error: error.message };
  }
}

// Check processing status
export async function checkProcessingStatus(predictionId: string) {
  try {
    const prediction = await replicate.predictions.get(predictionId);
    return {
      success: true,
      status: prediction.status,
      result: prediction.output,
      error: prediction.error,
    };
  } catch (error) {
    console.error('Status check failed:', error);
    return { success: false, error: error.message };
  }
}

// Utility function to validate image URL
export function isValidImageUrl(url: string): boolean {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  const urlLower = url.toLowerCase();
  return imageExtensions.some(ext => urlLower.includes(ext)) || url.startsWith('data:image/');
}

// Utility function to get processing cost estimate
export function getProcessingCostEstimate(operation: string): number {
  const costs = {
    'remove-background': 0.01,
    'replace-background': 0.03,
    'enhance-image': 0.02,
    'multi-angle-portrait': 0.04,
    'style-transfer': 0.02,
  };
  return costs[operation] || 0.02;
}
