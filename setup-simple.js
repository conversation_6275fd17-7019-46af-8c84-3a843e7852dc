#!/usr/bin/env node

/**
 * 简化的设置脚本 - 不需要 Stripe CLI
 * 用于快速设置开发环境
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
  console.log('🚀 Next.js SaaS Starter - 简化设置');
  console.log('=====================================\n');

  // 检查是否已有 .env 文件
  const envPath = path.join(process.cwd(), '.env');
  if (fs.existsSync(envPath)) {
    console.log('✅ .env 文件已存在');
    const content = fs.readFileSync(envPath, 'utf8');
    
    if (content.includes('***********************************')) {
      console.log('⚠️  检测到占位符数据库 URL');
      console.log('\n📋 下一步操作：');
      console.log('1. 获取免费 PostgreSQL 数据库：');
      console.log('   • Neon: https://neon.tech (推荐)');
      console.log('   • Supabase: https://supabase.com');
      console.log('2. 获取 Stripe 测试密钥：');
      console.log('   • https://dashboard.stripe.com/test/apikeys');
      console.log('3. 更新 .env 文件中的占位符值');
      console.log('4. 运行: npx pnpm db:migrate && npx pnpm db:seed');
    } else {
      console.log('✅ 环境变量看起来已配置');
    }
  } else {
    console.log('❌ 未找到 .env 文件');
  }

  console.log('\n🔧 当前 .env 文件状态：');
  if (fs.existsSync(envPath)) {
    const content = fs.readFileSync(envPath, 'utf8');
    const lines = content.split('\n').filter(line => 
      line.trim() && !line.startsWith('#')
    );
    
    lines.forEach(line => {
      const [key] = line.split('=');
      const hasPlaceholder = line.includes('your_') || 
                           line.includes('username:password@host') ||
                           line.includes('placeholder');
      console.log(`  ${hasPlaceholder ? '⚠️ ' : '✅'} ${key}`);
    });
  }

  rl.close();
}

main().catch(console.error);
