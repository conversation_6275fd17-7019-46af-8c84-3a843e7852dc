'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, Users } from 'lucide-react';
import Link from 'next/link';

// FAQ Item Component
const FAQItem = ({ question, answer, isOpen, onToggle }: {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}) => (
  <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-stone-200/50 overflow-hidden">
    <button
      onClick={onToggle}
      className="w-full p-6 text-left flex items-center justify-between hover:bg-stone-50/50 transition-colors duration-200"
    >
      <h3 className="text-stone-800 font-semibold pr-4">
        {question}
      </h3>
      <ChevronDown 
        className={`w-5 h-5 text-stone-600 transition-transform duration-200 flex-shrink-0 ${
          isOpen ? 'rotate-180' : ''
        }`}
      />
    </button>
    {isOpen && (
      <div className="px-6 pb-6">
        <p className="text-stone-600 leading-relaxed">
          {answer}
        </p>
      </div>
    )}
  </div>
);

export default function FAQSection() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqData = [
    {
      question: "What makes our AI image edit platform different from other tools?",
      answer: "Our AI image edit platform combines advanced AI with intuitive natural language processing, allowing you to edit images by simply describing what you want. No complex tools or design skills required. We offer one-shot AI image edit capabilities, character consistency, and ultra-fast AI image edit processing that delivers professional results in seconds."
    },
    {
      question: "How fast is the AI image edit processing?",
      answer: "Most AI image edit operations are completed within 15-30 seconds, depending on complexity. Our optimized AI image edit infrastructure ensures rapid turnaround without compromising quality. Batch AI image edit processing is even more efficient, handling multiple images simultaneously."
    },
    {
      question: "What file formats and sizes are supported for AI image edit?",
      answer: "Our AI image edit tool supports JPG, PNG, WebP, and TIFF formats up to 20MB per image. AI image edit output resolution can go up to 4K (4096x4096 pixels) while maintaining exceptional quality. For larger files or special formats, our Pro AI image edit plan offers extended support."
    },
    {
      question: "Is there a free AI image edit trial available?",
      answer: "Yes! We offer a 14-day free AI image edit trial with 50 image edits included. No credit card required to start using our AI image edit features. You can explore all AI image edit capabilities and see the quality of our AI before committing to a paid plan."
    },
    {
      question: "Can I use the AI image edit results commercially?",
      answer: "Absolutely! All images processed through our AI image edit platform come with full commercial usage rights. You own the AI image edit output and can use it for any purpose, including commercial projects, marketing materials, and client work with our AI image edit tool."
    },
    {
      question: "How secure is my data with the AI image edit platform?",
      answer: "We take security seriously with our AI image edit service. All uploads are encrypted in transit and at rest. Images are processed securely through our AI image edit system and automatically deleted from our servers after 30 days. We never use your images for training or any other purpose without explicit consent."
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 relative">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100/80 backdrop-blur-sm rounded-full border border-amber-200/60 mb-6">
            <Users className="w-4 h-4 text-amber-600" />
            <span className="text-sm text-amber-700 font-medium">Frequently Asked</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
            AI Image Edit Questions & Answers
          </h2>
          <p className="text-xl text-stone-600 max-w-3xl mx-auto">
            Everything you need to know about our AI image edit platform and AI image edit features
          </p>
        </div>

        <div className="space-y-4">
          {faqData.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openFAQ === index}
              onToggle={() => setOpenFAQ(openFAQ === index ? null : index)}
            />
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-stone-600 mb-6">Still have questions?</p>
          <Button asChild variant="outline" className="border-amber-300 text-amber-700 hover:bg-amber-50">
            <Link href="/contact">
              <Users className="w-4 h-4 mr-2" />
              Contact Support
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
