import { Metadata } from 'next';
import { getUser } from '@/lib/db/queries';
import { redirect } from 'next/navigation';
import AIImageEditor from '@/components/ai/AIImageEditor';

export const metadata: Metadata = {
  title: 'AI Image Editor - CreativeAI Studio',
  description: 'Edit images with AI-powered tools. Remove backgrounds, enhance quality, and transform your images with natural language commands.',
};

export default async function AIEditorPage() {
  const user = await getUser();

  if (!user) {
    redirect('/sign-in');
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">AI Image Editor</h2>
        <div className="flex items-center space-x-2">
          <div className="text-sm text-muted-foreground">
            Plan: <span className="font-medium capitalize">{user.subscription?.plan || 'free'}</span>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Transform Your Images with AI</h3>
            <p className="text-sm text-muted-foreground">
              Upload an image and use our AI-powered tools to edit, enhance, and transform it. 
              From background removal to style transfer, create stunning visuals with simple commands.
            </p>
          </div>
        </div>

        <AIImageEditor user={user} />
      </div>
    </div>
  );
}
