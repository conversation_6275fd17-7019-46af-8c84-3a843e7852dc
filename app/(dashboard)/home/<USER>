'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Image, 
  Zap, 
  Crown, 
  Upload, 
  History, 
  Users, 
  TrendingUp, 
  Calendar,
  Sparkles,
  Camera,
  Wand2,
  Download,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { Metadata } from 'next';

export default function DashboardHome() {
  // Mock data - in real app, this would come from API
  const stats = {
    imagesEdited: 127,
    creditsLeft: 450,
    totalCredits: 1000,
    currentPlan: 'Pro Plan',
    planStatus: 'Active',
    nextBilling: 'Jan 15, 2025',
    recentEdits: [
      { id: 1, name: 'product-photo.jpg', type: 'Background Removal', date: '2 hours ago' },
      { id: 2, name: 'portrait.png', type: 'Style Transfer', date: '5 hours ago' },
      { id: 3, name: 'landscape.jpg', type: 'Enhancement', date: '1 day ago' },
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 p-4 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-stone-800 mb-2">
              Welcome back! 👋
            </h1>
            <p className="text-lg text-stone-600">
              Ready to create some amazing content today?
            </p>
          </div>
          <div className="mt-4 lg:mt-0">
            <Button className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-6 py-3 rounded-xl shadow-lg">
              <Upload className="w-5 h-5 mr-2" />
              Upload & Edit
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-stone-600 font-medium">Images Edited</p>
                  <p className="text-3xl font-bold text-stone-800">{stats.imagesEdited}</p>
                  <p className="text-xs text-emerald-600 flex items-center mt-1">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    +12% this month
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-amber-100 to-orange-100 rounded-full flex items-center justify-center">
                  <Image className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-stone-600 font-medium">Current Plan</p>
                  <p className="text-xl font-semibold text-stone-800">{stats.currentPlan}</p>
                  <p className="text-xs text-stone-500 mt-1">{stats.planStatus}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-amber-100 to-orange-100 rounded-full flex items-center justify-center">
                  <Crown className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-stone-600 font-medium">Credits Left</p>
                  <p className="text-3xl font-bold text-stone-800">{stats.creditsLeft}</p>
                  <div className="mt-2">
                    <Progress 
                      value={(stats.creditsLeft / stats.totalCredits) * 100} 
                      className="h-2" 
                    />
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-amber-100 to-orange-100 rounded-full flex items-center justify-center">
                  <Zap className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-stone-600 font-medium">Next Billing</p>
                  <p className="text-lg font-semibold text-stone-800">{stats.nextBilling}</p>
                  <p className="text-xs text-stone-500 mt-1">Auto-renewal</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-amber-100 to-orange-100 rounded-full flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50">
          <CardHeader>
            <CardTitle className="text-stone-800 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-amber-600" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Jump right into your most-used features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button asChild className="h-20 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <Link href="/editor">
                  <div className="text-center">
                    <Upload className="w-6 h-6 mx-auto mb-2" />
                    <span className="text-sm font-medium">Upload & Edit</span>
                  </div>
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 border-stone-200 hover:bg-stone-50 rounded-xl transition-all duration-300">
                <Link href="/history">
                  <div className="text-center">
                    <History className="w-6 h-6 mx-auto mb-2 text-stone-600" />
                    <span className="text-sm font-medium text-stone-700">Recent Edits</span>
                  </div>
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 border-stone-200 hover:bg-stone-50 rounded-xl transition-all duration-300">
                <Link href="/templates">
                  <div className="text-center">
                    <Wand2 className="w-6 h-6 mx-auto mb-2 text-stone-600" />
                    <span className="text-sm font-medium text-stone-700">Templates</span>
                  </div>
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 border-stone-200 hover:bg-stone-50 rounded-xl transition-all duration-300">
                <Link href="/analytics">
                  <div className="text-center">
                    <BarChart3 className="w-6 h-6 mx-auto mb-2 text-stone-600" />
                    <span className="text-sm font-medium text-stone-700">Analytics</span>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity & Usage Stats */}
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Recent Edits */}
          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50">
            <CardHeader>
              <CardTitle className="text-stone-800 flex items-center gap-2">
                <History className="w-5 h-5 text-amber-600" />
                Recent Edits
              </CardTitle>
              <CardDescription>
                Your latest image editing activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentEdits.map((edit) => (
                  <div key={edit.id} className="flex items-center justify-between p-3 bg-stone-50 rounded-lg hover:bg-stone-100 transition-colors duration-200">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-amber-100 to-orange-100 rounded-lg flex items-center justify-center">
                        <Camera className="w-5 h-5 text-amber-600" />
                      </div>
                      <div>
                        <p className="font-medium text-stone-800">{edit.name}</p>
                        <p className="text-sm text-stone-500">{edit.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-stone-500">{edit.date}</p>
                      <Button size="sm" variant="ghost" className="text-amber-600 hover:text-amber-700">
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-stone-200">
                <Button asChild variant="outline" className="w-full">
                  <Link href="/history">
                    View All Edits
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Usage Overview */}
          <Card className="bg-white/80 backdrop-blur-sm border border-stone-200/50">
            <CardHeader>
              <CardTitle className="text-stone-800 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-amber-600" />
                Usage Overview
              </CardTitle>
              <CardDescription>
                Your monthly usage and limits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-stone-700">Images Processed</span>
                    <span className="text-sm text-stone-500">127 / 1000</span>
                  </div>
                  <Progress value={12.7} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-stone-700">Storage Used</span>
                    <span className="text-sm text-stone-500">2.3 GB / 10 GB</span>
                  </div>
                  <Progress value={23} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-stone-700">API Calls</span>
                    <span className="text-sm text-stone-500">1,247 / 5,000</span>
                  </div>
                  <Progress value={24.9} className="h-2" />
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t border-stone-200">
                <Button asChild className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600">
                  <Link href="/pricing">
                    <Crown className="w-4 h-4 mr-2" />
                    Upgrade Plan
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
