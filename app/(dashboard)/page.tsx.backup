import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowRight, Sparkles, Wand2, Camera, Image, Type, Palette, Edit3, Download, Zap, Users, Star, Play, Check, Upload, Loader, Crown, History, UserPlus, PlayCircle, Volume2, ChevronDown } from 'lucide-react';
import { Metadata } from 'next';
import NextImage from 'next/image';
import Link from 'next/link';
import FAQSection from './components/FAQSection';
import HomeAIEditor from '@/components/ai/HomeAIEditor';
import { getSession } from '@/lib/auth/session';

// Simple Badge component
const Badge = ({ children, className = "", variant = "default" }: {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "secondary" | "outline"
}) => {
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold";
  const variantClasses = {
    default: "bg-blue-600 text-white",
    secondary: "bg-gray-100 text-gray-800",
    outline: "border border-gray-300 text-gray-700"
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
};

export const metadata: Metadata = {
  title: "AI Image Edit - Professional AI Image Edit Tool & Editor Online",
  description: "Advanced AI image edit platform for professional photo editing. Use our AI image edit tool to transform, enhance, and edit images with artificial intelligence. Free AI image edit features available.",
  keywords: "ai image edit, AI image editor, ai image edit tool, ai image edit online, photo editing, background removal, image enhancement, artificial intelligence, batch processing, style transfer, object removal, AI photo editor, ai image edit free",
  authors: [{ name: "AI Image Edit Team" }],
  creator: "AI Image Edit",
  publisher: "AI Image Edit",
  robots: "index, follow",
  alternates: {
    canonical: "http://ai-image-editor.org/",
  },
  openGraph: {
    title: "AI Image Edit - Professional AI Image Edit Tool Online",
    description: "Transform your images with our powerful AI image edit platform. Advanced AI image edit features for background removal, enhancement, and artistic styles.",
    url: "http://ai-image-editor.org/",
    siteName: "AI Image Edit",
    type: "website",
    images: [
      {
        url: "http://ai-image-editor.org/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "AI Image Edit - Professional AI Image Edit Tool",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Image Edit - Professional AI Image Edit Tool Online",
    description: "Transform your images with our powerful AI image edit platform. Advanced AI image edit features for background removal and enhancement.",
    images: ["http://ai-image-editor.org/og-image.jpg"],
  },
};

export default async function HomePage() {
  const session = await getSession();
  const user = session?.user;

  return (
    <main className="bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 min-h-screen relative overflow-hidden">
      {/* Premium Background Grid */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(120,113,108,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(120,113,108,0.08)_1px,transparent_1px)] bg-[size:60px_60px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>

      {/* Sophisticated Light Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[1000px] h-[800px] bg-gradient-radial from-amber-200/20 via-orange-200/10 to-transparent blur-3xl animate-pulse"></div>
        <div className="absolute top-1/3 right-0 w-[700px] h-[500px] bg-gradient-radial from-rose-200/15 to-transparent blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-0 left-0 w-[600px] h-[400px] bg-gradient-radial from-amber-300/20 to-transparent blur-3xl animate-pulse delay-2000"></div>

        {/* Floating orbs */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-amber-200/15 rounded-full blur-2xl animate-float"></div>
        <div className="absolute top-40 right-32 w-24 h-24 bg-orange-200/15 rounded-full blur-2xl animate-float delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-28 h-28 bg-rose-200/15 rounded-full blur-2xl animate-float delay-2000"></div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-20 pb-20 px-4 sm:px-6 lg:px-8 z-10">
        <div className="max-w-7xl mx-auto">
          {/* Trust Indicators */}
          <div className="text-center mb-8 animate-fade-in-up">
            <div className="inline-flex items-center gap-8 px-8 py-4 bg-white/60 backdrop-blur-2xl rounded-full border border-stone-200/50 shadow-2xl hover:bg-white/70 transition-all duration-500">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse shadow-lg shadow-emerald-500/30"></div>
                <span className="text-sm font-semibold text-stone-700">Trusted by 50,000+ creators</span>
              </div>
              <div className="w-px h-5 bg-stone-300"></div>
              <div className="flex items-center gap-2">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-amber-400 text-amber-400 drop-shadow-sm" />
                ))}
                <span className="text-sm font-semibold text-stone-700 ml-2">4.9/5</span>
              </div>
            </div>
          </div>

          {/* Hero Content */}
          <div className="text-center mb-16">
            <div className="animate-fade-in-up delay-200">
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-stone-800 mb-6 leading-[0.85] tracking-tight">
                Professional<br />
                <span className="bg-gradient-to-r from-amber-600 via-orange-500 to-rose-500 bg-clip-text text-transparent relative inline-block">
                  AI Image Edit
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 via-orange-400/20 to-rose-400/20 blur-3xl -z-10"></div>
                </span><br />
                <span className="text-stone-700 text-4xl md:text-5xl lg:text-6xl">Tool & Editor</span>
              </h1>
            </div>

            <div className="animate-fade-in-up delay-400">
              <p className="text-xl md:text-2xl text-stone-600 mb-10 max-w-4xl mx-auto leading-relaxed font-light">
                Transform your images with our advanced AI image edit platform. Use our AI image edit tool to remove backgrounds, enhance photos,
                apply artistic styles, and process images in batches. Experience professional
                <span className="text-stone-800 font-semibold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent"> AI image edit capabilities</span> with cutting-edge technology.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Video Demo Section */}
      <section className="relative pt-8 pb-12 px-4 sm:px-6 lg:px-8 z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-stone-800 mb-4">
              See AI Image Edit in Action
            </h2>
            <p className="text-lg text-stone-600 mb-8">
              Watch how our AI image edit tool transforms images with simple text commands. Experience real-time AI image edit processing.
            </p>
          </div>

          {/* Video Container */}
          <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-amber-200/50 shadow-2xl">
            <div className="aspect-video bg-gradient-to-br from-stone-100 to-stone-200 rounded-2xl flex items-center justify-center relative overflow-hidden">
              {/* Placeholder for video */}
              <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 via-orange-500/10 to-rose-500/10"></div>
              <div className="text-center z-10">
                <div className="w-20 h-20 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 mx-auto shadow-lg hover:scale-110 transition-transform duration-300 cursor-pointer">
                  <Play className="w-8 h-8 text-amber-600 ml-1" />
                </div>
                <p className="text-stone-700 font-semibold">Click to play demo video</p>
                <p className="text-sm text-stone-500 mt-2">See real-time AI image editing</p>
              </div>

              {/* Video element (hidden by default, will be shown when video is loaded) */}
              <video
                className="absolute inset-0 w-full h-full object-cover rounded-2xl opacity-0 transition-opacity duration-500"
                controls
                poster="/video-thumbnail.jpg"
                preload="metadata"
              >
                <source src="/demo-video.mp4" type="video/mp4" />
                <source src="/demo-video.webm" type="video/webm" />
                Your browser does not support the video tag.
              </video>
            </div>

            {/* Video Features */}
            <div className="grid md:grid-cols-3 gap-6 mt-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Zap className="w-6 h-6 text-amber-600" />
                </div>
                <h3 className="font-semibold text-stone-800 mb-2">Lightning Fast AI Image Edit</h3>
                <p className="text-sm text-stone-600">AI image edit processing in seconds</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Wand2 className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-stone-800 mb-2">Natural Language AI Image Edit</h3>
                <p className="text-sm text-stone-600">AI image edit with simple text commands</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Star className="w-6 h-6 text-rose-600" />
                </div>
                <h3 className="font-semibold text-stone-800 mb-2">Professional AI Image Edit Results</h3>
                <p className="text-sm text-stone-600">Studio-quality AI image edit output</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Instant Experience Section */}
      <section className="py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100/80 backdrop-blur-sm rounded-full border border-amber-200/60 mb-6">
              <Sparkles className="w-4 h-4 text-amber-600" />
              <span className="text-sm text-amber-700 font-medium">Try It Now</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
              Experience AI Image Edit
              <br />
              <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                Instantly
              </span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto mb-12">
              Upload an image and describe what you want to edit. Our AI image edit tool will transform it in seconds with advanced AI image edit technology.
            </p>
          </div>

          {/* Interactive Demo */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-amber-200/50 shadow-2xl">
            <HomeAIEditor user={user} />
          </div>

          {/* Quick Examples */}
          <div className="mt-8 pt-8 border-t border-stone-200">
            <p className="text-sm font-semibold text-stone-700 mb-4">Quick examples to try:</p>
            <div className="flex flex-wrap gap-2">
              {[
                "Remove background",
                "Change to cartoon style",
                "Add professional lighting",
                "Make it black and white",
                "Add sunset background",
                "Enhance colors"
              ].map((example, index) => (
                <button
                  key={index}
                  className="px-4 py-2 bg-amber-50 hover:bg-amber-100 text-amber-700 rounded-full text-sm border border-amber-200 hover:border-amber-300 transition-all duration-300 cursor-pointer"
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-12 animate-fade-in-up delay-1000">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100/80 backdrop-blur-sm rounded-full border border-amber-200/60 mb-6">
              <Wand2 className="w-4 h-4 text-amber-600" />
              <span className="text-sm text-amber-700 font-medium">AI-Powered Features</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
              Professional AI image edit
              <br />
              <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                powered by AI
              </span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Transform your images with our advanced AI image edit platform. From background removal
              to artistic style transfer, our AI image edit tool handles complex editing tasks instantly with professional AI image edit capabilities.
            </p>
          </div>

          <div className="space-y-12 lg:space-y-16">

            {/* Feature 1: Smart Background Replacement - Before/After */}
            <div className="group animate-fade-in-up delay-1200">
              <div className="relative bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-2xl rounded-3xl border border-stone-200/40 p-6 lg:p-12 hover:border-amber-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-amber-200/30 hover:bg-white/90">
                <div className="absolute -top-4 -left-4 lg:-top-6 lg:-left-6">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-amber-400 via-orange-400 to-rose-400 rounded-3xl flex items-center justify-center shadow-2xl shadow-amber-300/40 group-hover:shadow-amber-400/60 transition-all duration-500 group-hover:scale-110">
                    <span className="text-white font-bold text-xl lg:text-2xl">01</span>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                  {/* Feature Image */}
                  <div className="relative group-hover:scale-[1.02] transition-transform duration-700">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-300/30 to-orange-300/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="relative bg-gradient-to-br from-white/90 to-stone-50/80 rounded-3xl p-6 border border-stone-200/40 backdrop-blur-sm shadow-xl">
                      <NextImage
                        src="/Intelligent Backgrounds.png"
                        alt="AI Background Replacement"
                        width={600}
                        height={400}
                        className="rounded-2xl w-full h-auto shadow-2xl"
                      />
                    </div>
                  </div>

                  {/* Feature Description */}
                  <div className="pt-6 lg:pt-8">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-gradient-to-r from-amber-100/80 to-orange-100/80 rounded-full border border-amber-200/60 mb-6 backdrop-blur-sm">
                      <Wand2 className="w-5 h-5 text-amber-600" />
                      <span className="text-sm font-semibold text-amber-700">Smart Background</span>
                    </div>

                    <h3 className="text-2xl lg:text-3xl font-bold text-stone-800 mb-4 leading-tight">
                      AI Image Edit
                      <br />
                      <span className="text-4xl lg:text-5xl bg-gradient-to-r from-amber-600 via-orange-600 to-stone-700 bg-clip-text text-transparent font-black">
                        Background Transform
                      </span>
                    </h3>

                    <p className="text-base text-stone-600 mb-6 leading-relaxed">
                      Replace backgrounds with our AI image edit tool using natural language commands. Our AI image edit technology understands context
                      and creates photorealistic results that match lighting and perspective perfectly with advanced AI image edit processing.
                    </p>

                    <div className="space-y-4 mb-8">
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Natural language editing</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-stone-600 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Photorealistic results</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-stone-600 to-amber-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Perfect lighting match</span>
                      </div>
                    </div>

                    <Button asChild className="bg-gradient-to-r from-amber-500 via-orange-500 to-stone-600 hover:from-amber-600 hover:via-orange-600 hover:to-stone-700 text-white font-semibold px-8 py-4 rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-400/40 text-lg">
                      <Link href="/sign-up">
                        Try Background Transform
                        <ArrowRight className="ml-3 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 2: Background Removal - Image LEFT, Text RIGHT */}
            <div className="group animate-fade-in-up delay-1400 lg:ml-12">
              <div className="relative bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-2xl rounded-3xl border border-stone-200/40 p-6 lg:p-12 hover:border-rose-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-rose-200/30 hover:bg-white/90">
                <div className="absolute -top-4 -right-4 lg:-top-6 lg:-right-6">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-amber-400 via-orange-400 to-stone-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-amber-300/40 group-hover:shadow-amber-400/60 transition-all duration-500 group-hover:scale-110">
                    <span className="text-white font-bold text-xl lg:text-2xl">02</span>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                  <div className="relative group-hover:scale-[1.02] transition-transform duration-700 lg:order-0">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-300/30 to-orange-300/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="relative bg-gradient-to-br from-white/90 to-stone-50/80 rounded-3xl p-6 border border-stone-200/40 backdrop-blur-sm shadow-xl">
                      <NextImage
                        src="/Smart Auto Enhancement.png"
                        alt="AI Auto Retouch"
                        width={600}
                        height={400}
                        className="rounded-2xl w-full h-auto shadow-2xl"
                      />
                    </div>
                  </div>

                  <div className="pt-8 lg:pt-12 lg:order-1">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-amber-100/80 rounded-full border border-amber-200/60 mb-8 backdrop-blur-sm">
                      <Wand2 className="w-5 h-5 text-amber-600" />
                      <span className="text-sm font-semibold text-amber-700">Smart Enhancement</span>
                    </div>

                    <h3 className="text-3xl lg:text-4xl font-bold text-stone-800 mb-6 leading-tight">
                      <span className="text-5xl lg:text-6xl bg-gradient-to-r from-amber-600 via-orange-600 to-stone-700 bg-clip-text text-transparent font-black block">
                        AI Image Edit Enhancement
                      </span>
                      <span className="text-2xl lg:text-3xl font-medium">
                        & Smart Processing
                      </span>
                    </h3>

                    <p className="text-lg text-stone-600 mb-8 leading-relaxed">
                      Automatically enhance portraits and create stunning scenes with our AI image edit platform.
                      Perfect skin retouching and intelligent background replacement using advanced AI image edit technology.
                    </p>

                    <div className="space-y-5 mb-10">
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Smart skin retouching</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-stone-600 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Scene replacement</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-stone-600 to-amber-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Natural lighting</span>
                      </div>
                    </div>

                    <Button asChild className="bg-gradient-to-r from-amber-500 via-orange-500 to-stone-600 hover:from-amber-600 hover:via-orange-600 hover:to-stone-700 text-white font-semibold px-8 py-4 rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-400/40 text-lg">
                      <Link href="/sign-up">
                        Try Enhancement
                        <ArrowRight className="ml-3 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 3: Object Removal - Text LEFT, Image RIGHT */}
            <div className="group animate-fade-in-up delay-1600 lg:mr-12">
              <div className="relative bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-2xl rounded-3xl border border-stone-200/40 p-8 lg:p-16 hover:border-amber-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-amber-200/30 hover:bg-white/90">
                <div className="absolute -top-4 -left-4 lg:-top-6 lg:-left-6">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-amber-400 via-orange-400 to-stone-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-amber-300/40 group-hover:shadow-amber-400/60 transition-all duration-500 group-hover:scale-110">
                    <span className="text-white font-bold text-xl lg:text-2xl">03</span>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                  <div className="pt-8 lg:pt-12">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-amber-100/80 rounded-full border border-amber-200/60 mb-8 backdrop-blur-sm">
                      <Wand2 className="w-5 h-5 text-amber-600" />
                      <span className="text-sm font-semibold text-amber-700">Smart Processing</span>
                    </div>

                    <h3 className="text-2xl lg:text-3xl font-bold text-stone-800 mb-4 leading-tight">
                      <span className="text-4xl lg:text-5xl bg-gradient-to-r from-amber-600 via-orange-600 to-stone-700 bg-clip-text text-transparent font-black block">
                        Smart Enhancement
                      </span>
                      <span className="text-3xl lg:text-4xl font-bold">
                        & AI Processing
                      </span>
                    </h3>

                    <p className="text-base text-stone-600 mb-8 leading-relaxed">
                      Transform portraits with intelligent retouching and create perfect scenes.
                      AI-powered beauty enhancement and background magic.
                    </p>

                    <div className="space-y-5 mb-10">
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Professional retouching</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-stone-600 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Dynamic backgrounds</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-stone-600 to-amber-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Lighting adjustment</span>
                      </div>
                    </div>

                    <Button asChild className="bg-gradient-to-r from-amber-500 via-orange-500 to-stone-600 hover:from-amber-600 hover:via-orange-600 hover:to-stone-700 text-white font-semibold px-8 py-4 rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-400/40 text-lg">
                      <Link href="/sign-up">
                        Try Smart Processing
                        <ArrowRight className="ml-3 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>

                  <div className="relative group-hover:scale-[1.02] transition-transform duration-700">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-300/30 to-orange-300/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="relative bg-gradient-to-br from-white/90 to-stone-50/80 rounded-3xl p-6 border border-stone-200/40 backdrop-blur-sm shadow-xl">
                      <NextImage
                        src="/image-text-edit.png"
                        alt="Smart Processing"
                        width={600}
                        height={400}
                        className="rounded-2xl w-full h-auto shadow-2xl"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 4: Style Transfer - Image LEFT, Text RIGHT */}
            <div className="group animate-fade-in-up delay-1800 lg:ml-12">
              <div className="relative bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-2xl rounded-3xl border border-stone-200/40 p-8 lg:p-16 hover:border-amber-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-amber-200/30 hover:bg-white/90">
                <div className="absolute -top-4 -right-4 lg:-top-6 lg:-right-6">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-amber-400 via-orange-400 to-stone-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-amber-300/40 group-hover:shadow-amber-400/60 transition-all duration-500 group-hover:scale-110">
                    <span className="text-white font-bold text-xl lg:text-2xl">04</span>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                  <div className="relative group-hover:scale-[1.02] transition-transform duration-700 lg:order-0">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-300/30 to-orange-300/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="relative bg-gradient-to-br from-white/90 to-stone-50/80 rounded-3xl p-6 border border-stone-200/40 backdrop-blur-sm shadow-xl">
                      <NextImage
                        src="/Creative Merge & Combine.png"
                        alt="AI Image Merging"
                        width={600}
                        height={400}
                        className="rounded-2xl w-full h-auto shadow-2xl"
                      />
                    </div>
                  </div>

                  <div className="pt-8 lg:pt-12 lg:order-1">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-amber-100/80 rounded-full border border-amber-200/60 mb-8 backdrop-blur-sm">
                      <Sparkles className="w-5 h-5 text-amber-600" />
                      <span className="text-sm font-semibold text-amber-700">Smart Creation</span>
                    </div>

                    <h3 className="text-3xl lg:text-4xl font-bold text-stone-800 mb-6 leading-tight">
                      <span className="text-2xl lg:text-3xl font-medium block">
                        AI Image Edit Merge
                      </span>
                      <span className="text-4xl lg:text-5xl bg-gradient-to-r from-amber-600 via-orange-600 to-stone-700 bg-clip-text text-transparent font-black">
                        Smart Creations
                      </span>
                    </h3>

                    <p className="text-lg text-stone-600 mb-8 leading-relaxed">
                      Combine multiple images seamlessly with our AI image edit tool to create stunning product visuals.
                      Perfect for e-commerce and marketing materials using advanced AI image edit capabilities.
                    </p>

                    <div className="space-y-5 mb-10">
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Smart composition</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-stone-600 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Seamless blending</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-stone-600 to-amber-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Product mockups</span>
                      </div>
                    </div>

                    <Button asChild className="bg-gradient-to-r from-amber-500 via-orange-500 to-stone-600 hover:from-amber-600 hover:via-orange-600 hover:to-stone-700 text-white font-semibold px-8 py-4 rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-400/40 text-lg">
                      <Link href="/sign-up">
                        Create Smart Images
                        <ArrowRight className="ml-3 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 5: Batch Processing - Text LEFT, Image RIGHT */}
            <div className="group animate-fade-in-up delay-2000 lg:mr-12">
              <div className="relative bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-2xl rounded-3xl border border-stone-200/40 p-8 lg:p-16 hover:border-amber-300/60 transition-all duration-700 hover:shadow-2xl hover:shadow-amber-200/30 hover:bg-white/90">
                <div className="absolute -top-4 -left-4 lg:-top-6 lg:-left-6">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-amber-400 via-orange-400 to-stone-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-amber-300/40 group-hover:shadow-amber-400/60 transition-all duration-500 group-hover:scale-110">
                    <span className="text-white font-bold text-xl lg:text-2xl">05</span>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                  <div className="pt-8 lg:pt-12">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-amber-100/80 rounded-full border border-amber-200/60 mb-8 backdrop-blur-sm">
                      <Zap className="w-5 h-5 text-amber-600" />
                      <span className="text-sm font-semibold text-amber-700">Multi-Angle</span>
                    </div>

                    <h3 className="text-2xl lg:text-3xl font-bold text-stone-800 mb-4 leading-tight">
                      <span className="text-5xl lg:text-6xl bg-gradient-to-r from-amber-600 via-orange-600 to-stone-700 bg-clip-text text-transparent font-black block">
                        AI Multi-Angle Edit
                      </span>
                      <span className="text-3xl lg:text-4xl font-bold">
                        Portrait
                      </span>
                    </h3>

                    <p className="text-base text-stone-600 mb-8 leading-relaxed">
                      Generate multiple portrait angles from a single photo using our AI image edit technology. Perfect for
                      professional headshots and social media profiles with advanced AI image edit processing.
                    </p>

                    <div className="space-y-5 mb-10">
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">360° angle generation</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-stone-600 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Professional poses</span>
                      </div>
                      <div className="flex items-center gap-4 group/item">
                        <div className="w-3 h-3 bg-gradient-to-r from-stone-600 to-amber-500 rounded-full group-hover/item:scale-125 transition-transform duration-300"></div>
                        <span className="text-stone-700 font-medium">Consistent lighting</span>
                      </div>
                    </div>

                    <Button asChild className="bg-gradient-to-r from-amber-500 via-orange-500 to-stone-600 hover:from-amber-600 hover:via-orange-600 hover:to-stone-700 text-white font-semibold px-8 py-4 rounded-2xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-400/40 text-lg">
                      <Link href="/sign-up">
                        Generate Multi-Angle
                        <ArrowRight className="ml-3 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>

                  <div className="relative group-hover:scale-[1.02] transition-transform duration-700">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-300/30 to-orange-300/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="relative bg-gradient-to-br from-white/90 to-stone-50/80 rounded-3xl p-6 border border-stone-200/40 backdrop-blur-sm shadow-xl">
                      <NextImage
                        src="/Multi-Angle Generation.png"
                        alt="Multi-Angle Portrait Generation"
                        width={600}
                        height={400}
                        className="rounded-2xl w-full h-auto shadow-2xl"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 animate-fade-in-up delay-1800">
            <h2 className="text-3xl md:text-4xl font-bold text-stone-800 mb-4">
              Trusted AI Image Edit Platform Worldwide
            </h2>
            <p className="text-lg text-stone-600">
              Join thousands of professionals who use our AI image edit tool to create stunning content daily with advanced AI image edit features
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 animate-fade-in-up delay-2000">
            <div className="text-center group">
              <div className="bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-xl rounded-2xl border border-stone-200/40 p-8 hover:border-amber-300/60 transition-all duration-500 hover:shadow-xl hover:shadow-amber-200/20">
                <div className="text-4xl lg:text-5xl font-bold text-stone-800 mb-2 group-hover:text-amber-600 transition-colors duration-300">
                  50K+
                </div>
                <div className="text-stone-600 font-medium">Active Users</div>
              </div>
            </div>

            <div className="text-center group">
              <div className="bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-xl rounded-2xl border border-stone-200/40 p-8 hover:border-orange-300/60 transition-all duration-500 hover:shadow-xl hover:shadow-orange-200/20">
                <div className="text-4xl lg:text-5xl font-bold text-stone-800 mb-2 group-hover:text-orange-600 transition-colors duration-300">
                  10M+
                </div>
                <div className="text-stone-600 font-medium">Images Created</div>
              </div>
            </div>

            <div className="text-center group">
              <div className="bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-xl rounded-2xl border border-stone-200/40 p-8 hover:border-emerald-300/60 transition-all duration-500 hover:shadow-xl hover:shadow-emerald-200/20">
                <div className="text-4xl lg:text-5xl font-bold text-stone-800 mb-2 group-hover:text-emerald-600 transition-colors duration-300">
                  99.9%
                </div>
                <div className="text-stone-600 font-medium">Uptime</div>
              </div>
            </div>

            <div className="text-center group">
              <div className="bg-gradient-to-br from-white/80 to-stone-50/60 backdrop-blur-xl rounded-2xl border border-stone-200/40 p-8 hover:border-rose-300/60 transition-all duration-500 hover:shadow-xl hover:shadow-rose-200/20">
                <div className="text-4xl lg:text-5xl font-bold text-stone-800 mb-2 group-hover:text-rose-600 transition-colors duration-300">
                  4.9★
                </div>
                <div className="text-stone-600 font-medium">User Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted by Leading Marketing Specialists */}
      <section className="py-16 bg-gradient-to-br from-stone-100 via-amber-50 to-orange-50 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-amber-200/10 via-transparent to-orange-200/10"></div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h2 className="text-2xl md:text-3xl font-bold text-stone-800 mb-12 animate-fade-in-up" style={{animationDelay: '1700ms'}}>
            Leading Marketing Specialists Trust Our AI Image Edit Platform
          </h2>

          <div className="grid md:grid-cols-3 gap-6">
            <Card className="bg-white/80 backdrop-blur-sm border border-amber-200/50 text-stone-800 group hover:bg-white/90 transition-all duration-500 transform hover:scale-105 hover:shadow-xl animate-fade-in-up" style={{animationDelay: '1900ms'}}>
              <CardContent className="p-5">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mr-3 group-hover:animate-pulse">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-sm group-hover:text-amber-600 transition-colors duration-300 text-stone-800">Lightning Fast AI Image Edit</h3>
                    <p className="text-xs text-stone-600">AI image edit in seconds</p>
                  </div>
                </div>
                <p className="text-stone-700 text-xs text-left leading-relaxed">
                  Create professional marketing materials 10x faster than traditional methods with our AI image edit platform.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border border-amber-200/50 text-stone-800 group hover:bg-white/90 transition-all duration-500 transform hover:scale-105 hover:shadow-xl animate-fade-in-up" style={{animationDelay: '2100ms'}}>
              <CardContent className="p-5">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mr-3 group-hover:animate-pulse">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-sm group-hover:text-amber-600 transition-colors duration-300 text-stone-800">AI Image Edit Team Collaboration</h3>
                    <p className="text-xs text-stone-600">Share AI image edit projects seamlessly</p>
                  </div>
                </div>
                <p className="text-stone-700 text-xs text-left leading-relaxed">
                  Share AI image edit projects, get feedback, and collaborate with your team in real-time on creative campaigns.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border border-amber-200/50 text-stone-800 group hover:bg-white/90 transition-all duration-500 transform hover:scale-105 hover:shadow-xl animate-fade-in-up" style={{animationDelay: '2300ms'}}>
              <CardContent className="p-5">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mr-3 group-hover:animate-pulse">
                    <Star className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-sm group-hover:text-amber-600 transition-colors duration-300 text-stone-800">Premium AI Image Edit Quality</h3>
                    <p className="text-xs text-stone-600">Professional AI image edit results</p>
                  </div>
                </div>
                <p className="text-stone-700 text-xs text-left leading-relaxed">
                  High-resolution AI image edit outputs ready for print, web, and social media across all platforms and formats.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-32 relative bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-amber-100/20 to-transparent"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20 animate-fade-in-up delay-2200">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-500/10 to-orange-500/10 backdrop-blur-sm rounded-full border border-amber-500/20 mb-6">
              <Zap className="w-4 h-4 text-amber-600" />
              <span className="text-sm text-amber-700 font-medium">Simple Pricing</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
              Choose your AI image edit
              <br />
              <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                perfect plan
              </span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Start with free AI image edit features, scale as you grow. No hidden fees, cancel anytime.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            {/* Starter Plan */}
            <div className="group animate-fade-in-up delay-2400">
              <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-amber-200/50 p-8 hover:border-amber-500/30 transition-all duration-500 hover:shadow-xl hover:shadow-amber-500/10 h-full">
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 px-3 py-1 bg-amber-500/20 rounded-full border border-amber-500/30 mb-6">
                    <span className="text-sm text-amber-700 font-medium">Starter</span>
                  </div>

                  <div className="mb-8">
                    <div className="text-5xl font-bold text-stone-800 mb-2">Free</div>
                    <p className="text-stone-600">Perfect for getting started</p>
                  </div>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-amber-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-amber-600" />
                      </div>
                      <span className="text-stone-700">10 AI generations per month</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-amber-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-amber-600" />
                      </div>
                      <span className="text-stone-700">Basic templates</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-amber-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-amber-600" />
                      </div>
                      <span className="text-stone-700">Standard resolution</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-amber-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-amber-600" />
                      </div>
                      <span className="text-stone-700">Community support</span>
                    </li>
                  </ul>

                  <Button asChild className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white border-0 py-3 rounded-xl transform hover:scale-105 transition-all duration-300">
                    <Link href="/sign-up">Get Started Free</Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Pro Plan - Most Popular */}
            <div className="group animate-fade-in-up delay-2600 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                <div className="bg-gradient-to-r from-amber-600 to-orange-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                  Most Popular
                </div>
              </div>

              <div className="relative bg-gradient-to-br from-amber-500/10 to-orange-500/10 backdrop-blur-xl rounded-3xl border-2 border-amber-500/30 p-8 hover:border-amber-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-amber-500/20 transform hover:scale-105 h-full">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="text-center relative z-10">
                  <div className="inline-flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-amber-500/30 to-orange-500/30 rounded-full border border-amber-500/50 mb-6">
                    <span className="text-sm text-amber-700 font-medium">Professional</span>
                  </div>

                  <div className="mb-8">
                    <div className="flex items-baseline justify-center gap-2 mb-2">
                      <span className="text-5xl font-bold text-stone-800">$29</span>
                      <span className="text-stone-600">/month</span>
                    </div>
                    <p className="text-stone-600">Perfect for growing businesses</p>
                  </div>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-stone-700">500 AI generations per month</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-stone-700">Premium templates & styles</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-stone-700">4K resolution exports</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-stone-700">Priority support</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-stone-700">Advanced AI features</span>
                    </li>
                  </ul>

                  <Button asChild className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white font-semibold py-3 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <Link href="/sign-up">Get Started</Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="group animate-fade-in-up delay-2800">
              <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-amber-200/50 p-8 hover:border-orange-500/30 transition-all duration-500 hover:shadow-xl hover:shadow-orange-500/10 h-full">
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 px-3 py-1 bg-orange-500/20 rounded-full border border-orange-500/30 mb-6">
                    <span className="text-sm text-orange-700 font-medium">Enterprise</span>
                  </div>

                  <div className="mb-8">
                    <div className="flex items-baseline justify-center gap-2 mb-2">
                      <span className="text-5xl font-bold text-stone-800">$99</span>
                      <span className="text-stone-600">/month</span>
                    </div>
                    <p className="text-stone-600">For large teams and agencies</p>
                  </div>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-orange-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-orange-600" />
                      </div>
                      <span className="text-stone-700">Unlimited AI generations</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-orange-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-orange-600" />
                      </div>
                      <span className="text-stone-700">Custom brand templates</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-orange-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-orange-600" />
                      </div>
                      <span className="text-stone-700">8K resolution exports</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-orange-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-orange-600" />
                      </div>
                      <span className="text-stone-700">24/7 dedicated support</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-orange-500/20 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-orange-600" />
                      </div>
                      <span className="text-stone-700">Full API access</span>
                    </li>
                  </ul>

                  <Button asChild className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg">
                    <Link href="/sign-up">Contact Sales</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection />

      {/* Final CTA Section */}
      <section className="py-32 relative bg-gradient-to-br from-stone-100 via-amber-50 to-orange-50">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-200/20 via-orange-200/20 to-amber-200/20"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(245,158,11,0.15),transparent_70%)]"></div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <div className="animate-fade-in-up delay-3000">
            <h2 className="text-4xl md:text-5xl font-bold text-stone-800 mb-6 leading-tight">
              Ready to start AI image edit
              <br />
              <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                stunning content?
              </span>
            </h2>

            <p className="text-xl text-stone-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Join over 50,000 creators, marketers, and businesses who trust our AI image edit platform
              to create professional content that converts with advanced AI image edit technology.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button asChild size="lg" className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white font-semibold text-lg px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-amber-500/25">
                <Link href="/sign-up">
                  <Sparkles className="mr-2 h-5 w-5" />
                  Get Started
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-stone-300 text-stone-700 hover:bg-stone-100 text-lg px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-300 backdrop-blur-sm">
                <Link href="/pricing">
                  <Users className="mr-2 h-5 w-5" />
                  View Pricing
                </Link>
              </Button>
            </div>

            <div className="flex items-center justify-center gap-6 text-sm text-stone-500">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-amber-600" />
                <span>No credit card required</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-amber-600" />
                <span>14-day free trial</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-amber-600" />
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
