import { checkoutAction } from '@/lib/payments/actions';
import { Check } from 'lucide-react';
import { getStripePrices, getStripeProducts } from '@/lib/payments/stripe';
import { SubmitButton } from './submit-button';

// Prices are fresh for one hour max
export const revalidate = 3600;

export default async function PricingPage() {
  const [prices, products] = await Promise.all([
    getStripePrices(),
    getStripeProducts(),
  ]);

  const basicPlan = products.find((product) => product.name === 'AI Image Editor - Basic');
  const professionalPlan = products.find((product) => product.name === 'AI Image Editor - Professional');
  const businessesPlan = products.find((product) => product.name === 'AI Image Editor - Businesses');

  const basicPrice = prices.find((price) => price.productId === basicPlan?.id);
  const professionalPrice = prices.find((price) => price.productId === professionalPlan?.id);
  const businessesPrice = prices.find((price) => price.productId === businessesPlan?.id);

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Choose Your AI Image Editing Plan
        </h1>
        <p className="text-xl text-gray-600">
          Transform your images with powerful AI tools
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <PricingCard
          name="Basic"
          price={basicPrice?.unitAmount || 499}
          interval={basicPrice?.interval || 'month'}
          trialDays={basicPrice?.trialPeriodDays || 7}
          features={[
            '50 AI edits per month',
            'Text editing & translation',
            'Background replacement',
            'Basic image enhancement',
            'Email support',
          ]}
          priceId={basicPrice?.id}
          popular={false}
        />
        <PricingCard
          name="Professional"
          price={professionalPrice?.unitAmount || 1499}
          interval={professionalPrice?.interval || 'month'}
          trialDays={professionalPrice?.trialPeriodDays || 7}
          features={[
            '500 AI edits per month',
            'All Basic features',
            'Advanced beauty enhancement',
            'Multi-angle portrait generation',
            'Priority processing',
            'Live chat support',
          ]}
          priceId={professionalPrice?.id}
          popular={true}
        />
        <PricingCard
          name="Business"
          price={businessesPrice?.unitAmount || 4999}
          interval={businessesPrice?.interval || 'month'}
          trialDays={businessesPrice?.trialPeriodDays || 7}
          features={[
            'Unlimited AI edits',
            'All Professional features',
            'Custom AI model training',
            'API access',
            'Team collaboration tools',
            'Dedicated account manager',
            '24/7 phone support',
          ]}
          priceId={businessesPrice?.id}
          popular={false}
        />
      </div>
    </main>
  );
}

function PricingCard({
  name,
  price,
  interval,
  trialDays,
  features,
  priceId,
  popular = false,
}: {
  name: string;
  price: number;
  interval: string;
  trialDays: number;
  features: string[];
  priceId?: string;
  popular?: boolean;
}) {
  return (
    <div className={`relative pt-6 pb-8 px-6 rounded-2xl border-2 ${
      popular
        ? 'border-orange-500 bg-orange-50 shadow-xl scale-105'
        : 'border-gray-200 bg-white shadow-lg'
    }`}>
      {popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
            Most Popular
          </span>
        </div>
      )}

      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{name}</h2>
        <p className="text-sm text-gray-600 mb-4">
          {trialDays} day free trial
        </p>
        <div className="mb-6">
          <span className="text-5xl font-bold text-gray-900">
            ${price / 100}
          </span>
          <span className="text-xl text-gray-600 ml-1">
            /{interval}
          </span>
        </div>
      </div>

      <ul className="space-y-3 mb-8">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <Check className="h-5 w-5 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <span className="text-gray-700">{feature}</span>
          </li>
        ))}
      </ul>

      <form action={checkoutAction} className="w-full">
        <input type="hidden" name="priceId" value={priceId} />
        <SubmitButton />
      </form>
    </div>
  );
}
