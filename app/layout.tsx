import './globals.css';
import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { Manrope } from 'next/font/google';
import { getUser, getTeamForUser } from '@/lib/db/queries';
import { SWRConfig } from 'swr';

export const metadata: Metadata = {
  title: {
    default: "AI Image Editor - Professional Photo Editing with Artificial Intelligence",
    template: "%s | AI Image Editor"
  },
  description: "Transform your images with our powerful AI image editor. Remove backgrounds, enhance photos, apply artistic styles, and batch process images with cutting-edge artificial intelligence technology.",
  keywords: [
    "AI image editor",
    "photo editing",
    "background removal",
    "image enhancement",
    "artificial intelligence",
    "batch processing",
    "style transfer",
    "object removal",
    "AI photo editor",
    "image editing software",
    "photo enhancement",
    "AI-powered editing",
    "professional photo editing",
    "image processing",
    "photo retouching",
    "AI image enhancement",
    "smart photo editor",
    "automated image editing",
    "neural style transfer",
    "content-aware fill"
  ],
  authors: [{ name: "AI Image Editor Team" }],
  creator: "AI Image Editor",
  publisher: "AI Image Editor",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('http://ai-image-editor.org'),
  alternates: {
    canonical: 'http://ai-image-editor.org/',
  },
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: "AI Image Editor - Professional Photo Editing with AI",
    description: "Transform your images with our powerful AI image editor. Remove backgrounds, enhance photos, and apply artistic styles instantly.",
    url: 'http://ai-image-editor.org',
    siteName: 'AI Image Editor',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AI Image Editor - Professional Photo Editing',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "AI Image Editor - Professional Photo Editing with AI",
    description: "Transform your images with our powerful AI image editor. Remove backgrounds, enhance photos, and apply artistic styles instantly.",
    images: ['/og-image.jpg'],
    creator: '@ai_image_editor',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const viewport: Viewport = {
  maximumScale: 1
};

const manrope = Manrope({ subsets: ['latin'] });

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`bg-white dark:bg-gray-950 text-black dark:text-white ${manrope.className}`}
    >
      <body className="min-h-[100dvh] bg-gray-50">
        {/* JSON-LD Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "AI Image Editor",
              "description": "Professional AI-powered image editing software. Remove backgrounds, enhance photos, apply artistic styles, and batch process images with cutting-edge artificial intelligence.",
              "url": "http://ai-image-editor.org",
              "applicationCategory": "MultimediaApplication",
              "operatingSystem": "Web Browser",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "description": "Free tier available"
              },
              "creator": {
                "@type": "Organization",
                "name": "AI Image Editor"
              },
              "featureList": [
                "AI Photo Enhancement",
                "Background Removal",
                "Object Removal",
                "Artistic Style Transfer",
                "Batch Image Processing",
                "Smart Color Correction",
                "Noise Reduction",
                "Content-Aware Fill"
              ],
              "screenshot": "http://ai-image-editor.org/screenshot.jpg",
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.9",
                "ratingCount": "50000",
                "bestRating": "5",
                "worstRating": "1"
              }
            })
          }}
        />
        <SWRConfig
          value={{
            fallback: {
              // We do NOT await here
              // Only components that read this data will suspend
              '/api/user': getUser(),
              '/api/team': getTeamForUser()
            }
          }}
        >
          {children}
        </SWRConfig>
      </body>
    </html>
  );
}
