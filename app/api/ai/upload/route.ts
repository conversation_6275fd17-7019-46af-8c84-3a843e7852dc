import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/auth/session';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// File upload configuration
const MAX_FILE_SIZE = {
  free: 5 * 1024 * 1024, // 5MB
  pro: 20 * 1024 * 1024, // 20MB
  enterprise: 50 * 1024 * 1024, // 50MB
};

const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
];

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      );
    }

    // Check file size based on user plan
    const userPlan = user.subscription?.plan || 'free';
    const maxSize = MAX_FILE_SIZE[userPlan as keyof typeof MAX_FILE_SIZE] || MAX_FILE_SIZE.free;

    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return NextResponse.json(
        { error: `File too large. Maximum size for ${userPlan} plan is ${maxSizeMB}MB.` },
        { status: 413 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'ai-images');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${user.id}_${timestamp}_${randomString}.${fileExtension}`;
    const filePath = join(uploadDir, fileName);

    // Convert file to buffer and save
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Generate public URL
    const publicUrl = `/uploads/ai-images/${fileName}`;
    const fullUrl = `${process.env.BASE_URL || 'http://localhost:3000'}${publicUrl}`;

    // Return success response
    return NextResponse.json({
      success: true,
      url: fullUrl,
      publicPath: publicUrl,
      fileName,
      fileSize: file.size,
      fileType: file.type,
      uploadedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { error: 'File upload failed' },
      { status: 500 }
    );
  }
}

// Handle file deletion
export async function DELETE(request: NextRequest) {
  try {
    // Get user session
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { fileName } = body;

    if (!fileName) {
      return NextResponse.json(
        { error: 'No filename provided' },
        { status: 400 }
      );
    }

    // Validate that the file belongs to the user (check filename prefix)
    if (!fileName.startsWith(`${user.id}_`)) {
      return NextResponse.json(
        { error: 'Unauthorized file access' },
        { status: 403 }
      );
    }

    // Delete file
    const filePath = join(process.cwd(), 'public', 'uploads', 'ai-images', fileName);
    
    if (existsSync(filePath)) {
      const fs = await import('fs/promises');
      await fs.unlink(filePath);
    }

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully',
    });

  } catch (error) {
    console.error('File deletion error:', error);
    return NextResponse.json(
      { error: 'File deletion failed' },
      { status: 500 }
    );
  }
}

// Get upload limits for current user
export async function GET(request: NextRequest) {
  try {
    // Get user session
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userPlan = user.subscription?.plan || 'free';
    const maxSize = MAX_FILE_SIZE[userPlan as keyof typeof MAX_FILE_SIZE] || MAX_FILE_SIZE.free;

    return NextResponse.json({
      success: true,
      limits: {
        maxFileSize: maxSize,
        maxFileSizeMB: Math.round(maxSize / (1024 * 1024)),
        allowedTypes: ALLOWED_TYPES,
        plan: userPlan,
      },
    });

  } catch (error) {
    console.error('Get upload limits error:', error);
    return NextResponse.json(
      { error: 'Failed to get upload limits' },
      { status: 500 }
    );
  }
}
