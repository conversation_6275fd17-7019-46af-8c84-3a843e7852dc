import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { imageProcesses, usageStats } from '@/lib/db/schema';
import { eq, and, sql } from 'drizzle-orm';

// 使用量限制 - 匹配Stripe套餐
const USAGE_LIMITS = {
  free: { daily: 2, monthly: 5 },                                    // 免费用户：每天2次，每月5次
  'AI Image Editor - Basic': { daily: 5, monthly: 50 },              // Basic ($4.99): 每月50次
  'AI Image Editor - Professional': { daily: 20, monthly: 500 },     // Professional ($14.99): 每月500次
  'AI Image Editor - Businesses': { daily: -1, monthly: -1 },        // Business ($49.99): 无限制
  // 兼容旧的命名方式
  basic: { daily: 5, monthly: 50 },
  professional: { daily: 20, monthly: 500 },
  business: { daily: -1, monthly: -1 },
};

// AI模型配置 - 统一使用nano-banana模型
const AI_MODELS = {
  TEXT_EDIT: {
    model: 'google/nano-banana-edit',
    cost: 0.020,
    keywords: ['文字', '替换', '翻译', '修改', 'text', 'replace', 'translate', 'edit', 'change']
  },
  BACKGROUND_REPLACE: {
    model: 'google/nano-banana-edit',
    cost: 0.020,
    keywords: ['背景', '替换', '更换', 'background', 'replace', 'change', 'backdrop', 'scene']
  },
  ENHANCE_FACE: {
    model: 'google/nano-banana-edit',
    cost: 0.020,
    keywords: ['美颜', '增强', '提升', '画质', 'enhance', 'beauty', 'improve', 'quality', 'face']
  },
  PORTRAIT_GENERATE: {
    model: 'google/nano-banana-edit',
    cost: 0.020,
    keywords: ['人像', '头像', '生成', '角度', 'portrait', 'headshot', 'generate', 'angle', 'profile']
  }
};

// 智能判断使用哪个模型
function determineAIModel(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();
  
  // 检查文字编辑关键词
  if (AI_MODELS.TEXT_EDIT.keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'TEXT_EDIT';
  }
  
  // 检查背景替换关键词
  if (AI_MODELS.BACKGROUND_REPLACE.keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'BACKGROUND_REPLACE';
  }
  
  // 检查美颜增强关键词
  if (AI_MODELS.ENHANCE_FACE.keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'ENHANCE_FACE';
  }
  
  // 检查人像生成关键词
  if (AI_MODELS.PORTRAIT_GENERATE.keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'PORTRAIT_GENERATE';
  }
  
  // 默认使用背景替换（最通用的功能）
  return 'BACKGROUND_REPLACE';
}

// 处理不同类型的AI操作 - 统一使用nano-banana模型
async function processWithAI(imageUrl: string, modelType: string, prompt: string) {
  const modelConfig = AI_MODELS[modelType];
  const KIE_API_KEY = process.env.KIE_API_KEY;

  if (!KIE_API_KEY) {
    throw new Error('KIE API key not configured');
  }

  try {
    // 根据不同功能类型优化提示词，但都使用nano-banana模型
    let optimizedPrompt = prompt;

    switch (modelType) {
      case 'TEXT_EDIT':
        optimizedPrompt = `Edit the text in this image: ${prompt}`;
        break;
      case 'BACKGROUND_REPLACE':
        optimizedPrompt = `Change the background: ${prompt}`;
        break;
      case 'ENHANCE_FACE':
        optimizedPrompt = `Enhance the face and improve image quality: ${prompt}`;
        break;
      case 'PORTRAIT_GENERATE':
        optimizedPrompt = `Generate a professional portrait: ${prompt}`;
        break;
    }

    // 调用Kie.ai的nano-banana-edit API
    const response = await fetch('https://api.kie.ai/google/nano-banana-edit', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${KIE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: optimizedPrompt,
        image_urls: [imageUrl],
        num_images: 1
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Nano Banana API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }

    const result = await response.json();

    if (!result || !result.output || !Array.isArray(result.output) || result.output.length === 0) {
      throw new Error('Invalid response format from Nano Banana API');
    }

    return { success: true, result: result.output[0], cost: modelConfig.cost, model: modelType };
  } catch (error) {
    console.error(`AI处理失败 (${modelType}):`, error);
    return { success: false, error: error.message };
  }
}

// 检查使用量限制
async function checkUsageLimit(userId: string, userPlan: string): Promise<boolean> {
  const limits = USAGE_LIMITS[userPlan] || USAGE_LIMITS.free;
  
  if (limits.daily === -1) return true; // 无限制
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dailyUsage = await db
    .select({ count: sql<number>`count(*)` })
    .from(imageProcesses)
    .where(
      and(
        eq(imageProcesses.userId, userId),
        sql`${imageProcesses.createdAt} >= ${today}`
      )
    );
  
  return dailyUsage[0]?.count < limits.daily;
}

export async function POST(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { imageUrl, prompt } = await request.json();

    if (!imageUrl || !prompt) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 检查使用量限制
    const canUse = await checkUsageLimit(user.id, user.plan || 'free');
    if (!canUse) {
      return NextResponse.json({ 
        error: '已达到今日使用限制，请升级订阅计划' 
      }, { status: 429 });
    }

    // 智能判断使用哪个AI模型
    const modelType = determineAIModel(prompt);
    console.log(`智能选择模型: ${modelType} (基于提示词: "${prompt}")`);

    // 处理图片
    const result = await processWithAI(imageUrl, modelType, prompt);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // 记录处理历史
    await db.insert(imageProcesses).values({
      userId: user.id,
      originalImageUrl: imageUrl,
      processedImageUrl: result.result,
      operation: modelType.toLowerCase().replace('_', '-'),
      parameters: JSON.stringify({ prompt, modelType }),
      cost: result.cost,
      status: 'completed',
      createdAt: new Date(),
    });

    // 更新使用统计
    const today = new Date().toISOString().split('T')[0];
    await db
      .insert(usageStats)
      .values({
        userId: user.id,
        date: today,
        operationType: modelType.toLowerCase().replace('_', '-'),
        count: 1,
        totalCost: result.cost,
      })
      .onConflictDoUpdate({
        target: [usageStats.userId, usageStats.date, usageStats.operationType],
        set: {
          count: sql`${usageStats.count} + 1`,
          totalCost: sql`${usageStats.totalCost} + ${result.cost}`,
        },
      });

    return NextResponse.json({
      success: true,
      result: result.result,
      message: 'AI处理成功'
    });

  } catch (error) {
    console.error('智能AI处理错误:', error);
    return NextResponse.json(
      { error: '处理失败，请稍后重试' },
      { status: 500 }
    );
  }
}
