import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/auth/session';
import { db } from '@/lib/db/drizzle';
import { imageProcesses, usageStats } from '@/lib/db/schema';
import { eq, and, sql } from 'drizzle-orm';

// 使用量限制 - 匹配Stripe套餐
const USAGE_LIMITS = {
  free: { daily: 2, monthly: 5 },
  'AI Image Editor - Basic': { daily: 5, monthly: 50 },
  'AI Image Editor - Professional': { daily: 20, monthly: 500 },
  'AI Image Editor - Businesses': { daily: -1, monthly: -1 },
  // 兼容旧的命名方式
  basic: { daily: 5, monthly: 50 },
  professional: { daily: 20, monthly: 500 },
  business: { daily: -1, monthly: -1 },
};

// 智能判断功能类型
function determineEditType(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();
  
  // 文字编辑关键词
  const textKeywords = ['text', 'replace', 'translate', 'edit', 'change', 'word', 'letter', 'font'];
  // 背景替换关键词  
  const backgroundKeywords = ['background', 'replace', 'change', 'backdrop', 'scene', 'remove background'];
  // 美颜增强关键词
  const enhanceKeywords = ['enhance', 'beauty', 'improve', 'quality', 'face', 'skin', 'smooth'];
  // 人像生成关键词
  const portraitKeywords = ['portrait', 'headshot', 'generate', 'angle', 'profile', 'face'];
  
  if (textKeywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'text-edit';
  }
  if (backgroundKeywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'background-replace';
  }
  if (enhanceKeywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'enhance-face';
  }
  if (portraitKeywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'portrait-generate';
  }
  
  // 默认使用背景替换（最通用的功能）
  return 'background-replace';
}

// 调用nano-banana API
async function callNanoBananaAPI(imageUrl: string, prompt: string, editType: string) {
  const KIE_API_KEY = process.env.KIE_API_KEY;
  
  if (!KIE_API_KEY) {
    throw new Error('KIE API key not configured');
  }

  try {
    // 根据编辑类型优化提示词
    let optimizedPrompt = prompt;
    
    switch (editType) {
      case 'text-edit':
        optimizedPrompt = `Edit the text in this image: ${prompt}`;
        break;
      case 'background-replace':
        optimizedPrompt = `Change the background: ${prompt}`;
        break;
      case 'enhance-face':
        optimizedPrompt = `Enhance the face and improve image quality: ${prompt}`;
        break;
      case 'portrait-generate':
        optimizedPrompt = `Generate a professional portrait: ${prompt}`;
        break;
      default:
        optimizedPrompt = prompt;
    }

    // 调用Kie.ai的nano-banana-edit API
    const response = await fetch('https://api.kie.ai/google/nano-banana-edit', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${KIE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: optimizedPrompt,
        image_urls: [imageUrl],
        num_images: 1
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Nano Banana API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }

    const result = await response.json();
    
    // 检查结果格式
    if (!result || !result.output || !Array.isArray(result.output) || result.output.length === 0) {
      throw new Error('Invalid response format from Nano Banana API');
    }

    return {
      success: true,
      result: result.output[0], // 返回第一张生成的图片
      editType,
      cost: 0.020 // nano-banana的固定成本
    };

  } catch (error) {
    console.error('Nano Banana API call failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to process image with Nano Banana'
    };
  }
}

// 检查使用量限制
async function checkUsageLimit(userId: string, userPlan: string): Promise<boolean> {
  const limits = USAGE_LIMITS[userPlan] || USAGE_LIMITS.free;
  
  if (limits.daily === -1) return true; // 无限制
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dailyUsage = await db
    .select({ count: sql<number>`count(*)` })
    .from(imageProcesses)
    .where(
      and(
        eq(imageProcesses.userId, userId),
        sql`${imageProcesses.createdAt} >= ${today}`
      )
    );
  
  return dailyUsage[0]?.count < limits.daily;
}

export async function POST(request: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { imageUrl, prompt } = await request.json();

    if (!imageUrl || !prompt) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 检查使用量限制
    const canUse = await checkUsageLimit(user.id, user.plan || 'free');
    if (!canUse) {
      return NextResponse.json({ 
        error: '已达到今日使用限制，请升级订阅计划' 
      }, { status: 429 });
    }

    // 智能判断编辑类型
    const editType = determineEditType(prompt);
    console.log(`智能选择编辑类型: ${editType} (基于提示词: "${prompt}")`);

    // 调用nano-banana API处理图片
    const result = await callNanoBananaAPI(imageUrl, prompt, editType);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // 记录处理历史
    await db.insert(imageProcesses).values({
      userId: user.id,
      originalImageUrl: imageUrl,
      processedImageUrl: result.result,
      operation: editType,
      parameters: JSON.stringify({ prompt, editType, model: 'nano-banana' }),
      cost: result.cost,
      status: 'completed',
      createdAt: new Date(),
    });

    // 更新使用统计
    const today = new Date().toISOString().split('T')[0];
    await db
      .insert(usageStats)
      .values({
        userId: user.id,
        date: today,
        operationType: editType,
        count: 1,
        totalCost: result.cost,
      })
      .onConflictDoUpdate({
        target: [usageStats.userId, usageStats.date, usageStats.operationType],
        set: {
          count: sql`${usageStats.count} + 1`,
          totalCost: sql`${usageStats.totalCost} + ${result.cost}`,
        },
      });

    return NextResponse.json({
      success: true,
      result: result.result,
      message: 'AI处理成功'
    });

  } catch (error) {
    console.error('统一AI处理错误:', error);
    return NextResponse.json(
      { error: '处理失败，请稍后重试' },
      { status: 500 }
    );
  }
}
