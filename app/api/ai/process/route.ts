import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { db } from '@/lib/db/drizzle';
import { imageProcesses, usageStats } from '@/lib/db/schema';
import { eq, and, sql } from 'drizzle-orm';
import Replicate from 'replicate';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Usage limits by plan - 匹配Stripe套餐
const USAGE_LIMITS = {
  free: { daily: 2, monthly: 5 },                                    // 免费用户：每天2次，每月5次
  'AI Image Editor - Basic': { daily: 5, monthly: 50 },              // Basic ($4.99): 每月50次
  'AI Image Editor - Professional': { daily: 20, monthly: 500 },     // Professional ($14.99): 每月500次
  'AI Image Editor - Businesses': { daily: -1, monthly: -1 },        // Business ($49.99): 无限制
  // 兼容旧的命名方式
  basic: { daily: 5, monthly: 50 },
  professional: { daily: 20, monthly: 500 },
  business: { daily: -1, monthly: -1 },
};

// 🎯 AI Models - 统一使用qwen-image-edit模型
const AI_MODELS = {
  // 🥇 核心功能：文字编辑
  TEXT_EDIT: {
    model: 'qwen/qwen-image-edit',
    cost: 0.003,
    description: '智能编辑图片中的文字内容',
    category: 'text-editing',
    priority: 1
  },
  // 🥈 背景替换
  BACKGROUND_REPLACE: {
    model: 'qwen/qwen-image-edit',
    cost: 0.005,
    description: '用自然语言替换图片背景',
    category: 'background-editing',
    priority: 2
  },
  // 🥉 图像增强/美颜 - 现在也使用qwen-image-edit
  ENHANCE_FACE: {
    model: 'qwen/qwen-image-edit',
    cost: 0.008,
    description: '智能美颜和画质增强',
    category: 'enhancement',
    priority: 3
  },
  // 🥉 人像生成 - 现在也使用qwen-image-edit
  PORTRAIT_GENERATE: {
    model: 'qwen/qwen-image-edit',
    cost: 0.010,
    description: '生成多角度专业人像',
    category: 'portrait-generation',
    priority: 4
  }
};

// 🤖 统一的AI处理函数 - 全部使用qwen-image-edit模型
async function processWithQwenImageEdit(imageUrl: string, operation: string, parameters: any) {
  try {
    let prompt = '';

    switch (operation) {
      case 'text-edit':
        // 🥇 核心功能：文字编辑
        const { oldText, newText, language = 'zh' } = parameters;
        prompt = language === 'zh'
          ? `请将图片中的"${oldText}"替换为"${newText}"，保持原有的字体样式、颜色和位置不变`
          : `Replace "${oldText}" with "${newText}" in the image, maintaining the original font style, color and position`;
        break;

      case 'background-replace':
        // 🥈 背景替换
        const { background } = parameters;
        prompt = `将图片的背景替换为：${background}，保持主体物体不变，确保光照和透视自然协调`;
        break;

      case 'enhance-face':
        // 🥉 美颜功能 - 现在也使用qwen-image-edit
        prompt = `Enhance and beautify the face in this image, improve skin texture, remove blemishes, enhance facial features naturally while maintaining the original appearance`;
        break;

      case 'portrait-generate':
        // 🥉 人像生成 - 现在也使用qwen-image-edit
        const { angle = 'front view' } = parameters;
        prompt = `Generate a professional headshot portrait, ${angle}, studio lighting, high quality, professional photography style`;
        break;

      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }

    // 检查图片URL是否可公开访问
    if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
      console.log('📍 Local image URL detected in /api/ai/process, using enhanced simulation');
      return NextResponse.json({
        success: true,
        result: `${imageUrl}?ai_effect=enhanced&operation=${operation}&t=${Date.now()}`,
        message: `🎨 AI Processing simulation completed! Operation: ${operation} (Upgrade to use real AI)`
      });
    }

    const output = await replicate.run("qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42", {
      input: {
        prompt: prompt,
        image: imageUrl,
        output_format: "webp",
        output_quality: 90,
        seed: Math.floor(Math.random() * 1000000)
      }
    });

    return { success: true, result: output };
  } catch (error) {
    console.error(`AI processing failed for ${operation}:`, error);
    return { success: false, error: error.message };
  }
}

// 旧的processWithReplicate函数已废弃，所有功能现在都使用qwen-image-edit模型

// 🎯 统一路由：所有操作都使用qwen-image-edit模型
async function processImage(imageUrl: string, operation: string, parameters: any = {}) {
  // 所有操作都使用qwen-image-edit模型处理
  if (['text-edit', 'background-replace', 'enhance-face', 'portrait-generate'].includes(operation)) {
    return await processWithQwenImageEdit(imageUrl, operation, parameters);
  }

  throw new Error(`Unsupported operation: ${operation}`);
}

// 获取处理成本估算
function getProcessingCostEstimate(operation: string): number {
  const operationMap = {
    'text-edit': AI_MODELS.TEXT_EDIT.cost,
    'background-replace': AI_MODELS.BACKGROUND_REPLACE.cost,
    'enhance-face': AI_MODELS.ENHANCE_FACE.cost,
    'portrait-generate': AI_MODELS.PORTRAIT_GENERATE.cost,
  };
  return operationMap[operation] || 0.01;
}

// 验证图片URL
function isValidImageUrl(url: string): boolean {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  const urlLower = url.toLowerCase();
  return imageExtensions.some(ext => urlLower.includes(ext)) || url.startsWith('data:image/');
}

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { imageUrl, operation, parameters = {} } = body;

    // Validate input
    if (!imageUrl || !operation) {
      return NextResponse.json(
        { error: 'Missing required fields: imageUrl and operation' },
        { status: 400 }
      );
    }

    if (!isValidImageUrl(imageUrl)) {
      return NextResponse.json(
        { error: 'Invalid image URL format' },
        { status: 400 }
      );
    }

    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    const currentMonth = new Date().toISOString().slice(0, 7);
    
    // Get user's subscription plan (assuming it's stored in user object)
    const userPlan = user.subscription?.plan || 'free';
    const limits = USAGE_LIMITS[userPlan as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.free;

    // Check daily usage
    if (limits.daily > 0) {
      const dailyUsage = await db
        .select({ count: sql<number>`count(*)` })
        .from(usageStats)
        .where(
          and(
            eq(usageStats.userId, user.id),
            eq(usageStats.featureType, 'ai-image-processing'),
            eq(usageStats.date, today)
          )
        );

      if (dailyUsage[0]?.count >= limits.daily) {
        return NextResponse.json(
          { error: 'Daily usage limit exceeded. Please upgrade your plan.' },
          { status: 429 }
        );
      }
    }

    // Check monthly usage
    if (limits.monthly > 0) {
      const monthlyUsage = await db
        .select({ count: sql<number>`count(*)` })
        .from(usageStats)
        .where(
          and(
            eq(usageStats.userId, user.id),
            eq(usageStats.featureType, 'ai-image-processing'),
            sql`date >= ${currentMonth}-01`
          )
        );

      if (monthlyUsage[0]?.count >= limits.monthly) {
        return NextResponse.json(
          { error: 'Monthly usage limit exceeded. Please upgrade your plan.' },
          { status: 429 }
        );
      }
    }

    // Create processing record
    const [processRecord] = await db
      .insert(imageProcesses)
      .values({
        userId: user.id,
        teamId: user.teamId || null,
        originalUrl: imageUrl,
        prompt: JSON.stringify({ operation, parameters }),
        status: 'processing',
        createdAt: new Date(),
      })
      .returning();

    // Process image with AI
    const result = await processImage(imageUrl, operation, parameters);

    if (!result.success) {
      // Update record with error
      await db
        .update(imageProcesses)
        .set({
          status: 'failed',
          processedUrl: null,
        })
        .where(eq(imageProcesses.id, processRecord.id));

      return NextResponse.json(
        { error: result.error || 'Image processing failed' },
        { status: 500 }
      );
    }

    // Update record with success
    await db
      .update(imageProcesses)
      .set({
        status: 'completed',
        processedUrl: Array.isArray(result.result) ? result.result[0] : result.result,
      })
      .where(eq(imageProcesses.id, processRecord.id));

    // Record usage statistics
    await db
      .insert(usageStats)
      .values({
        userId: user.id,
        teamId: user.teamId || null,
        featureType: 'ai-image-processing',
        usageCount: 1,
        date: today,
      })
      .onConflictDoUpdate({
        target: [usageStats.userId, usageStats.featureType, usageStats.date],
        set: {
          usageCount: sql`${usageStats.usageCount} + 1`,
        },
      });

    // Return success response
    return NextResponse.json({
      success: true,
      processId: processRecord.id,
      result: Array.isArray(result.result) ? result.result[0] : result.result,
      operation,
      estimatedCost: getProcessingCostEstimate(operation),
    });

  } catch (error) {
    console.error('AI processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const processId = searchParams.get('processId');

    if (!processId) {
      // Return user's processing history
      const processes = await db
        .select()
        .from(imageProcesses)
        .where(eq(imageProcesses.userId, user.id))
        .orderBy(sql`${imageProcesses.createdAt} DESC`)
        .limit(20);

      return NextResponse.json({
        success: true,
        processes: processes.map(process => ({
          id: process.id,
          originalUrl: process.originalUrl,
          processedUrl: process.processedUrl,
          operation: JSON.parse(process.prompt || '{}'),
          status: process.status,
          createdAt: process.createdAt,
        })),
      });
    }

    // Get specific process status
    const [process] = await db
      .select()
      .from(imageProcesses)
      .where(
        and(
          eq(imageProcesses.id, parseInt(processId)),
          eq(imageProcesses.userId, user.id)
        )
      );

    if (!process) {
      return NextResponse.json(
        { error: 'Process not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      process: {
        id: process.id,
        originalUrl: process.originalUrl,
        processedUrl: process.processedUrl,
        operation: JSON.parse(process.prompt || '{}'),
        status: process.status,
        createdAt: process.createdAt,
      },
    });

  } catch (error) {
    console.error('AI process status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
