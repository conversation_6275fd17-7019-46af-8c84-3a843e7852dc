import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';

// 创建一个演示版本的图片处理，给原图添加一个半透明的叠加层
export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get('url');
    const operation = request.nextUrl.searchParams.get('operation') || 'processed';
    
    if (!url) {
      return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
    }
    
    // 创建一个带有处理标识的响应
    // 对于演示版本，我们返回一个简单的HTML页面，显示"处理后"的图片
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI处理演示结果</title>
        <style>
            body { 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                font-family: Arial, sans-serif;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }
            .demo-badge {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-weight: bold;
                margin-bottom: 20px;
                display: inline-block;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            .image-container {
                position: relative;
                margin: 20px 0;
            }
            .image-container img {
                max-width: 100%;
                border-radius: 8px;
            }
            .overlay {
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(0, 255, 0, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            .message {
                background: #f8f9fa;
                padding: 15px;
                border-left: 4px solid #007bff;
                margin: 20px 0;
                border-radius: 4px;
            }
            .upgrade-btn {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 25px;
                font-weight: bold;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                margin-top: 20px;
                transition: transform 0.2s;
            }
            .upgrade-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="demo-badge">✨ AI处理演示版本</div>
            <h2>🎨 AI图片处理完成！</h2>
            
            <div class="image-container">
                <img src="${url}" alt="AI处理结果">
                <div class="overlay">✅ AI ${operation.toUpperCase()}</div>
            </div>
            
            <div class="message">
                <strong>演示模式说明：</strong><br>
                • 当前为试用演示版本<br>
                • 真实版本将生成全新的AI处理图片<br>
                • 支持背景替换、物体移除、风格转换等<br>
                • 升级后获得高质量AI处理结果
            </div>
            
            <a href="/" class="upgrade-btn">🚀 立即升级获得真实AI处理</a>
        </div>
    </body>
    </html>
    `;
    
    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
    
  } catch (error) {
    console.error('Demo process error:', error);
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
  }
}