import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

interface InstantIDRequest {
  imageUrl: string;
  prompt: string;
  negative_prompt?: string;
  num_steps?: number;
  style_strength_ratio?: number;
}

// InstantID API integration for portrait generation
export async function POST(request: NextRequest) {
  try {
    const { 
      imageUrl, 
      prompt, 
      negative_prompt = "bad quality, low quality, low resolution, blurry, distorted",
      num_steps = 30,
      style_strength_ratio = 20 
    }: InstantIDRequest = await request.json();

    if (!imageUrl || !prompt) {
      return NextResponse.json(
        { success: false, error: 'Missing required imageUrl and prompt parameters' },
        { status: 400 }
      );
    }

    const replicateToken = process.env.REPLICATE_API_TOKEN;
    if (!replicateToken || replicateToken === 'your_replicate_api_token_here') {
      console.log('Replicate API key not configured, using enhanced simulation mode');
      return await simulateInstantID(imageUrl, prompt);
    }

    try {
      const replicate = new Replicate({
        auth: replicateToken,
      });

      // Use InstantID model for portrait generation
      const output = await replicate.run(
        "zsxkib/instantid:dd2633d4c87b4c1b5a0e78d5926e3c00f92a8f6b96d90e82e5cf40c4fe3e5bd7",
        {
          input: {
            image: imageUrl,
            prompt: prompt,
            negative_prompt: negative_prompt,
            num_inference_steps: num_steps,
            style_strength_ratio: style_strength_ratio,
            guidance_scale: 5,
            seed: Math.floor(Math.random() * 1000000),
            enable_lcm_img2img: false
          }
        }
      ) as string[];

      if (output && output.length > 0) {
        const resultUrl = output[0];
        
        return NextResponse.json({
          success: true,
          result: resultUrl,
          message: `🎭 AI portrait generation completed successfully`,
          provider: 'instantid',
          parameters: {
            num_steps,
            style_strength_ratio,
            guidance_scale: 5
          }
        });
      } else {
        throw new Error('No valid output received from InstantID');
      }

    } catch (error) {
      console.error('InstantID API error:', error);
      // Fallback to enhanced simulation
      return await simulateInstantID(imageUrl, prompt);
    }

  } catch (error) {
    console.error('InstantID processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Processing failed, please try again' },
      { status: 500 }
    );
  }
}

async function simulateInstantID(imageUrl: string, prompt: string) {
  // Simulate processing time for InstantID (portrait generation typically takes longer)
  await new Promise(resolve => setTimeout(resolve, 8000 + Math.random() * 4000));
  
  // Create enhanced visual effects for portrait generation
  const filterEffect = 'saturate(1.2) contrast(1.15) brightness(1.03) hue-rotate(5deg)';
  
  // Return processed URL with portrait generation effects
  const processedUrl = `${imageUrl}?instantid_effect=${encodeURIComponent(filterEffect)}&operation=portrait&provider=instantid&t=${Date.now()}`;
  
  const resultMessage = `🎭 AI portrait generation simulation complete! Applied style: "${prompt}"`;
  
  return NextResponse.json({
    success: true,
    result: processedUrl,
    message: `${resultMessage} (Simulation mode - upgrade for real AI processing)`,
    provider: 'instantid-simulation',
    parameters: {
      num_steps: 30,
      style_strength_ratio: 20,
      simulated: true
    }
  });
}