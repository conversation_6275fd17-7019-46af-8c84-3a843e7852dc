import { NextRequest, NextResponse } from 'next/server';
import { 
  generateTrialFingerprint, 
  checkTrialUsage, 
  recordTrialUsage, 
  TRIAL_LIMITS 
} from '@/lib/trial-service';

// AI model configuration - 统一使用nano-banana模型
const AI_MODELS = {
  'background-replace': {
    model: 'replicate-background-models',
    cost: 0.020,
    keywords: [
      // 中文关键词
      '背景', '替换', '更换', '换背景', '环境', '场景', '商品', '抠图',
      // 英文关键词
      'background', 'replace', 'change', 'backdrop', 'scene', 'environment', 'setting', 
      'remove bg', 'new background', 'beach', 'studio', 'office', 'nature', 'sky',
      // 具体场景
      'forest', 'mountain', 'city', 'indoor', 'outdoor', 'professional', 'white background'
    ]
  },
  'text-edit': {
    model: 'qwen-image-edit',
    cost: 0.020,
    keywords: [
      // 中文关键词
      '文字', '替换', '翻译', '修改', '字', '对象', '物品', '添加', '删除', '移除',
      // 英文关键词
      'text', 'replace', 'translate', 'edit', 'change', 'object', 'item', 'add', 'remove',
      'microphone', 'flower', 'bouquet', 'hand', 'clothing', 'accessory', 'jewelry',
      // 编辑动作
      'swap', 'transform', 'modify', 'alter', 'substitute', 'delete', 'erase'
    ]
  },
  'enhance-face': {
    model: 'codeformer-face-enhancement',
    cost: 0.020,
    keywords: [
      // 中文关键词
      '美颜', '增强', '提升', '画质', '清晰', '脸部', '面部', '皮肤', '修复',
      // 英文关键词
      'enhance', 'beauty', 'improve', 'quality', 'face', 'facial', 'skin', 'clear',
      'sharp', 'restoration', 'upscale', 'denoise', 'smooth', 'professional',
      // 具体效果
      'skin texture', 'face quality', 'portrait quality', 'high resolution'
    ]
  },
  'portrait-generation': {
    model: 'photomaker-portrait',
    cost: 0.020,
    keywords: [
      // 中文关键词
      '人像', '风格', '肖像', '换脸', '转换', '艺术', '专业', '写真',
      // 英文关键词
      'portrait', 'style', 'person', 'character', 'artistic', 'professional', 
      'headshot', 'avatar', 'profile', 'studio portrait', 'fashion', 'glamour',
      // 风格类型
      'vintage', 'modern', 'classic', 'artistic style', 'business portrait'
    ]
  }
};

// Use the new trial service for consistent fingerprinting

// Intelligently determine which model to use
function determineAIModel(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();
  
  // Check portrait generation keywords (highest priority for specialized use)
  if (AI_MODELS['portrait-generation'].keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'portrait-generation';
  }
  
  // Check text editing keywords
  if (AI_MODELS['text-edit'].keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'text-edit';
  }
  
  // Check beauty enhancement keywords  
  if (AI_MODELS['enhance-face'].keywords.some(keyword => lowerPrompt.includes(keyword))) {
    return 'enhance-face';
  }
  
  // Default to background replacement (most common feature)
  return 'background-replace';
}

// Trial usage functions now handled by the trial service

// 模拟AI处理（在没有实际API密钥时使用）
async function simulateAIProcessing(imageUrl: string, operation: string, prompt: string) {
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
  
  // 为试用版创建一个更好的演示体验
  // 返回原图，但添加特殊标记，前端会显示叠加层
  const processedUrl = imageUrl + '?demo=true&operation=' + operation + '&t=' + Date.now();
  
  let resultMessage = '';
  switch (operation) {
    case 'background-replace':
      resultMessage = `🎨 AI背景替换模拟完成！提示词："${prompt}"`;
      break;
    case 'text-edit':
      resultMessage = `✏️ AI文字编辑模拟完成！提示词："${prompt}"`;
      break;
    case 'enhance-face':
      resultMessage = `✨ AI美颜增强模拟完成！提示词："${prompt}"`;
      break;
    default:
      resultMessage = `🤖 AI处理模拟完成！操作：${operation}，提示词："${prompt}"`;
  }
  
  return {
    success: true,
    result: processedUrl, // 带参数的URL表示"处理后"状态
    message: `${resultMessage}（试用版本 - 升级获得真实AI处理）`
  };
}

// 统一使用kie.ai的nano-banana模型处理所有操作
async function processWithNanoBanana(imageUrl: string, operation: string, prompt: string) {
  const modelConfig = AI_MODELS[operation as keyof typeof AI_MODELS];
  if (!modelConfig) {
    throw new Error(`Unknown operation: ${operation}`);
  }

  const KIE_API_KEY = process.env.KIE_API_KEY;

  if (!KIE_API_KEY) {
    console.log('KIE API key not configured, falling back to enhanced simulation');
    return await createEnhancedSimulation(imageUrl, operation, prompt);
  }

  try {
    // 根据操作类型优化提示词
    let optimizedPrompt = prompt;

    switch (operation) {
      case 'text-edit':
        optimizedPrompt = `Edit the text in this image: ${prompt}`;
        break;
      case 'background-replace':
        optimizedPrompt = `Change the background: ${prompt}`;
        break;
      case 'enhance-face':
        optimizedPrompt = `Enhance the face and improve image quality: ${prompt}`;
        break;
      case 'portrait-generation':
        optimizedPrompt = `Generate a professional portrait: ${prompt}`;
        break;
    }

    console.log(`Calling kie.ai nano-banana with prompt: ${optimizedPrompt}`);

    // 调用kie.ai的nano-banana模型
    console.log(`Attempting kie.ai API call with prompt: ${optimizedPrompt}`);
    
    // 尝试多个可能的端点和格式
    const endpoints = [
      {
        url: 'https://api.kie.ai/v1/predictions',
        body: {
          model: "google/nano-banana-edit",
          prompt: optimizedPrompt,
          image_urls: [imageUrl],
          num_images: "1"
        }
      },
      {
        url: 'https://api.kie.ai/v1/models/google/nano-banana-edit/predictions',
        body: {
          input: {
            prompt: optimizedPrompt,
            image_urls: [imageUrl],
            num_images: "1"
          }
        }
      },
      {
        url: 'https://api.kie.ai/predict',
        body: {
          model: "google/nano-banana-edit",
          input: {
            prompt: optimizedPrompt,
            image_urls: [imageUrl]
          }
        }
      },
      {
        url: 'https://api.kie.ai/run/google/nano-banana-edit',
        body: {
          input: {
            prompt: optimizedPrompt,
            image_urls: [imageUrl]
          }
        }
      }
    ];
    
    let lastError = null;
    
    for (let i = 0; i < endpoints.length; i++) {
      const endpoint = endpoints[i];
      console.log(`Trying endpoint ${i + 1}: ${endpoint.url}`);
      
      try {
        const response = await fetch(endpoint.url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${KIE_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(endpoint.body),
          signal: AbortSignal.timeout(15000), // 15秒超时
        });

        if (response.ok) {
          console.log(`✅ kie.ai API call successful with endpoint ${i + 1}!`);
          const result = await response.json();
          console.log('kie.ai API response:', result);
          
          // 处理kie.ai响应格式
          let outputUrl = null;
          if (result.output && Array.isArray(result.output) && result.output.length > 0) {
            outputUrl = result.output[0];
          } else if (result.output && typeof result.output === 'string') {
            outputUrl = result.output;
          } else if (result.data && Array.isArray(result.data) && result.data.length > 0) {
            outputUrl = result.data[0].url || result.data[0];
          } else if (result.images && Array.isArray(result.images) && result.images.length > 0) {
            outputUrl = result.images[0];
          } else if (result.result && typeof result.result === 'string') {
            outputUrl = result.result;
          }

          if (outputUrl) {
            return {
              success: true,
              result: outputUrl,
              message: `AI processing completed using kie.ai nano-banana-edit model`
            };
          } else {
            console.error('Invalid kie.ai response format:', result);
            lastError = new Error('Invalid response format from kie.ai API');
          }
        } else {
          const errorText = await response.text();
          console.log(`Endpoint ${i + 1} failed: ${response.status} - ${errorText.substring(0, 100)}`);
          lastError = new Error(`kie.ai API error: ${response.status}`);
        }
      } catch (fetchError) {
        console.log(`Endpoint ${i + 1} network error: ${fetchError.message}`);
        lastError = fetchError;
      }
    }
    
    // 如果所有端点都失败了，抛出最后一个错误
    console.log(`All kie.ai endpoints failed. Last error: ${lastError?.message}`);
    throw lastError || new Error('All kie.ai endpoints failed');

  } catch (error) {
    console.error(`Kie.ai nano-banana error for ${operation}:`, error);
    // Fallback to enhanced simulation
    return await createEnhancedSimulation(imageUrl, operation, prompt);
  }
}

// 使用Replicate API进行真实AI处理
async function processWithRealAI(imageUrl: string, operation: string, prompt: string) {
  const replicateToken = process.env.REPLICATE_API_TOKEN;
  
  if (!replicateToken || replicateToken === 'your_replicate_api_token_here') {
    console.log('Replicate API key not configured, falling back to enhanced simulation');
    return await createEnhancedSimulation(imageUrl, operation, prompt);
  }

  try {
    // 动态导入Replicate
    let Replicate;
    try {
      Replicate = (await import('replicate')).default;
    } catch (importError) {
      console.error('Failed to import Replicate:', importError);
      return await createEnhancedSimulation(imageUrl, operation, prompt);
    }

    const replicate = new Replicate({
      auth: replicateToken,
    });

    let output;
    let resultMessage = '';

    switch (operation) {
      case 'background-replace':
        // 背景替换 - 使用图像编辑模型直接修改背景
        try {
          console.log(`🎨 Processing background replacement: "${prompt}"`);
          
          // 检查图片URL是否可公开访问
          if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
            console.log('📍 Local image URL detected, using simulation mode');
            throw new Error('Local image URLs not accessible to external APIs');
          }
          
          output = await replicate.run(
            "qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42",
            {
              input: {
                prompt: buildPromptForOperation(prompt, operation),
                image: imageUrl,
                output_format: "webp",
                output_quality: 90
              }
            }
          );
          resultMessage = `🎨 AI Background Replacement completed! Prompt: "${prompt}"`;
        } catch (bgError) {
          console.log('Background replacement model failed:', bgError);
          throw bgError;
        }
        break;

      case 'text-edit':
        // 文本和对象编辑 - 使用 Qwen Image Edit 模型
        try {
          console.log(`✏️ Processing object/text editing: "${prompt}"`);
          
          // 检查图片URL是否可公开访问
          if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
            console.log('📍 Local image URL detected, using simulation mode');
            throw new Error('Local image URLs not accessible to external APIs');
          }
          
          output = await replicate.run(
            "qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42",
            {
              input: {
                prompt: buildPromptForOperation(prompt, operation),
                image: imageUrl,
                output_format: "webp",
                output_quality: 90
              }
            }
          );
          resultMessage = `✏️ AI Object Edit completed! Prompt: "${prompt}"`;
        } catch (editError) {
          console.log('Text/Object edit model failed:', editError);
          throw editError;
        }
        break;

      case 'enhance-face':
        // 面部增强和美化
        try {
          console.log(`✨ Processing face enhancement: "${prompt}"`);
          // 使用 Real-ESRGAN 进行图像超分辨率增强
          output = await replicate.run(
            "nightmareai/real-esrgan",
            {
              input: {
                image: imageUrl,
                scale: 2,
                face_enhance: true
              }
            }
          );
          resultMessage = `✨ AI Face Enhancement completed! Prompt: "${prompt}"`;
        } catch (faceError) {
          console.log('Face enhancement model failed, trying general enhancement:', faceError);
          // 回退到通用图像编辑
          output = await replicate.run(
            "qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42",
            {
              input: {
                prompt: `Enhance face quality, improve skin texture, professional portrait: ${prompt}`,
                image: imageUrl,
                output_format: "webp",
                output_quality: 90
              }
            }
          );
          resultMessage = `✨ AI Image Enhancement completed! Prompt: "${prompt}"`;
        }
        break;

      case 'portrait-generation':
        // 人像风格转换和生成
        try {
          console.log(`🎭 Processing portrait generation: "${prompt}"`);
          // 使用通用的图像编辑模型进行人像风格转换
          output = await replicate.run(
            "qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42",
            {
              input: {
                prompt: buildPromptForOperation(prompt, operation),
                image: imageUrl,
                output_format: "webp",
                output_quality: 90
              }
            }
          );
          resultMessage = `🎭 AI Portrait Generation completed! Prompt: "${prompt}"`;
        } catch (portraitError) {
          console.log('Portrait generation model failed, using general image edit:', portraitError);
          throw portraitError;
        }
        break;

      default:
        // 通用图像编辑
        try {
          console.log(`🎨 Processing general image edit: "${prompt}"`);
          output = await replicate.run(
            "qwen/qwen-image-edit:f1d0e682b391956e6e8399320775082e4511adf1f2f0f2250d823dae5fa5ff42",
            {
              input: {
                prompt: buildPromptForOperation(prompt, operation),
                image: imageUrl,
                output_format: "webp",
                output_quality: 90
              }
            }
          );
          resultMessage = `🎨 AI Image Edit completed! Prompt: "${prompt}"`;
        } catch (generalError) {
          console.log('General image edit failed:', generalError);
          throw generalError;
        }
        break;
    }

    // 处理输出结果
    let resultUrl = '';
    if (typeof output === 'string') {
      resultUrl = output;
    } else if (Array.isArray(output) && output.length > 0) {
      resultUrl = output[0];
    } else if (output && typeof output === 'object' && output.output) {
      resultUrl = output.output;
    } else {
      throw new Error('无效的AI模型输出格式');
    }

    if (!resultUrl || typeof resultUrl !== 'string') {
      throw new Error('未收到有效的AI处理结果');
    }

    return { 
      success: true, 
      result: resultUrl,
      message: `${resultMessage}（真实AI处理）`,
      provider: getProviderName(operation),
      realAI: true
    };
    
  } catch (error) {
    console.error(`Real AI processing error (${operation}):`, error);
    
    // 如果真实AI调用失败，回退到增强的模拟模式
    console.log('Falling back to enhanced simulation mode due to AI API error');
    return await createEnhancedSimulation(imageUrl, operation, prompt);
  }
}

// 根据操作构建提示词 - 针对AI模型优化
function buildPromptForOperation(userPrompt: string, operation: string): string {
  switch (operation) {
    case 'background-replace':
      // 背景替换 - 保持主体不变，只改变背景
      return `Replace the background with: ${userPrompt}. Keep all people, objects, and foreground elements unchanged. Only modify the background. Seamless integration, photorealistic quality, professional lighting match.`;
    
    case 'text-edit':
      // 对象和文本编辑 - 精确修改指定元素
      return `Edit the image: ${userPrompt}. Keep the overall composition and style unchanged. Make precise modifications only to the specified elements. High quality, natural appearance.`;
    
    case 'enhance-face':
      // 面部增强 - 提高质量但保持自然
      return `Enhance facial features: ${userPrompt}. Improve skin texture, clarity, and natural beauty. Maintain original facial structure and natural appearance. Professional portrait quality.`;
    
    case 'portrait-generation':
      // 人像风格转换
      return `Transform into professional portrait style: ${userPrompt}. High quality studio lighting, artistic composition, maintain facial features while enhancing overall presentation.`;
    
    default:
      // 通用编辑
      return `Image editing request: ${userPrompt}. Maintain image quality and natural appearance. Make precise edits while preserving the overall composition and style.`;
  }
}

// 获取提供商名称
function getProviderName(operation: string): string {
  switch (operation) {
    case 'background-replace':
    case 'text-edit':
    case 'enhance-face':
    case 'portrait-generation':
      return 'qwen-image-edit';
    default:
      return 'qwen-image-edit';
  }
}

// 创建增强的模拟效果 - 提供更真实的AI处理演示
async function createEnhancedSimulation(imageUrl: string, operation: string, prompt: string) {
  try {
    // 模拟真实AI处理时间
    const processingTime = 3000 + Math.random() * 2000;
    await new Promise(resolve => setTimeout(resolve, processingTime));
    
    console.log(`Demo processing: ${operation} with prompt: "${prompt}"`);
    
    // 基于操作类型和提示词生成更智能的处理效果
    let effectParameters = '';
    let processingDescription = '';
    
    switch (operation) {
      case 'background-replace':
        if (prompt.toLowerCase().includes('beach')) {
          effectParameters = 'hue-rotate(15deg) saturate(1.4) brightness(1.1) contrast(1.05)';
          processingDescription = 'Background replaced with beach scene';
        } else if (prompt.toLowerCase().includes('studio') || prompt.toLowerCase().includes('professional')) {
          effectParameters = 'brightness(1.05) contrast(1.15) saturate(0.95) sepia(0.05)';
          processingDescription = 'Professional studio background applied';
        } else {
          effectParameters = 'hue-rotate(25deg) saturate(1.3) brightness(1.08)';
          processingDescription = 'Background environment changed';
        }
        break;
        
      case 'text-edit':
        if (prompt.toLowerCase().includes('microphone') && prompt.toLowerCase().includes('flower')) {
          effectParameters = 'brightness(1.08) saturate(1.2) contrast(1.1) hue-rotate(10deg)';
          processingDescription = 'Object replacement: microphone → flowers';
        } else {
          effectParameters = 'brightness(1.06) contrast(1.12) saturate(1.1)';
          processingDescription = 'Content editing completed';
        }
        break;
        
      case 'enhance-face':
        effectParameters = 'brightness(1.04) saturate(1.08) contrast(1.06) blur(0.3px)';
        processingDescription = 'Facial enhancement and skin smoothing';
        break;
        
      default:
        effectParameters = 'saturate(1.15) brightness(1.05) contrast(1.05)';
        processingDescription = 'AI processing completed';
    }
    
    // 创建带有智能处理参数的URL
    const processedUrl = `${imageUrl}?demo=enhanced&operation=${operation}&effect=${encodeURIComponent(effectParameters)}&desc=${encodeURIComponent(processingDescription)}&t=${Date.now()}`;
    
    const resultMessage = `🤖 ${processingDescription}! Prompt: "${prompt}" (Free Trial Demo - Upgrade for real AI processing)`;
    
    return {
      success: true,
      result: processedUrl,
      message: resultMessage
    };
    
  } catch (error) {
    console.error('Enhanced simulation error:', error);
    return await simulateAIProcessing(imageUrl, operation, prompt);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, prompt } = await request.json();

    if (!imageUrl || !prompt) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 生成试用指纹
    const fingerprint = generateTrialFingerprint(request);

    // 检查试用次数限制
    const usageCheck = await checkTrialUsage(fingerprint);
    
    if (!usageCheck.allowed) {
      return NextResponse.json({
        success: false,
        error: 'Daily trial uses exhausted',
        trialLimitReached: true,
        trialExhausted: true,
        remaining: usageCheck.remaining,
        resetTime: usageCheck.resetTime,
        message: 'Please sign up for more uses'
      }, { status: 429 });
    }

    // 智能判断使用哪个AI模型
    const operation = determineAIModel(prompt);
    console.log(`Trial mode: Smart operation selection ${operation} (based on prompt: "${prompt}")`);

    // 处理图片 - 优先使用 Replicate API 进行真实 AI 处理
    let result;
    try {
      console.log('🚀 Using Replicate API for real AI processing...');
      result = await processWithRealAI(imageUrl, operation, prompt);
      console.log('✅ Replicate API succeeded!');
    } catch (replicateError) {
      console.log('❌ Replicate API failed, falling back to demo mode');
      console.error('Replicate error details:', replicateError);
      result = await createEnhancedSimulation(imageUrl, operation, prompt);
    }

    if (!result.success) {
      return NextResponse.json({ 
        success: false, 
        error: result.error || 'AI processing failed' 
      }, { status: 500 });
    }

    // 记录试用使用
    await recordTrialUsage(fingerprint, operation, prompt);

    // 获取更新后的剩余次数
    const updatedUsage = await checkTrialUsage(fingerprint);

    return NextResponse.json({
      success: true,
      result: result.result,
      message: result.message,
      trial: {
        remaining: updatedUsage.remaining,
        dailyLimit: TRIAL_LIMITS.dailyLimit,
        operation: operation,
        fingerprint: fingerprint.substring(0, 8) // Only show first 8 chars for privacy
      }
    });

  } catch (error) {
    console.error('Trial AI processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Processing failed, please try again later' },
      { status: 500 }
    );
  }
}

// 获取试用状态
export async function GET(request: NextRequest) {
  try {
    const fingerprint = generateTrialFingerprint(request);
    const usageCheck = await checkTrialUsage(fingerprint);

    return NextResponse.json({
      success: true,
      trial: {
        dailyLimit: TRIAL_LIMITS.dailyLimit,
        remaining: usageCheck.remaining,
        used: TRIAL_LIMITS.dailyLimit - usageCheck.remaining,
        resetTime: usageCheck.resetTime,
        fingerprint: fingerprint.substring(0, 8), // Only show first 8 chars for privacy
        allowedOperations: TRIAL_LIMITS.allowedOperations
      }
    });

  } catch (error) {
    console.error('Get trial status error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get trial status' },
      { status: 500 }
    );
  }
}