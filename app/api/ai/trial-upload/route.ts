import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { generateTrialFingerprint, TRIAL_LIMITS } from '@/lib/trial-service';

const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
];

export async function POST(request: NextRequest) {
  try {
    // 解析表单数据
    const formData = await request.formData();
    const file = formData.get('image') as File; // 注意这里使用 'image' 字段名

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      );
    }

    // 检查文件大小
    if (file.size > TRIAL_LIMITS.maxFileSize) {
      const maxSizeMB = Math.round(TRIAL_LIMITS.maxFileSize / (1024 * 1024));
      return NextResponse.json(
        { success: false, error: `File too large. Maximum size for trial is ${maxSizeMB}MB.` },
        { status: 413 }
      );
    }

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'trial-images');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // 生成唯一文件名
    const fingerprint = generateTrialFingerprint(request);
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `trial_${fingerprint.substring(0, 8)}_${timestamp}_${randomString}.${fileExtension}`;
    const filePath = join(uploadDir, fileName);

    // 保存文件
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // 生成公共URL
    const publicUrl = `/uploads/trial-images/${fileName}`;
    const fullUrl = `${process.env.BASE_URL || 'http://localhost:3001'}${publicUrl}`;

    // 返回成功响应 - 注意字段名与HomeAIEditor期望的一致
    return NextResponse.json({
      success: true,
      imageUrl: fullUrl, // HomeAIEditor期望的字段名
      url: fullUrl, // 兼容字段
      publicPath: publicUrl,
      fileName,
      fileSize: file.size,
      fileType: file.type,
      uploadedAt: new Date().toISOString(),
      isTrial: true,
      fingerprint: fingerprint.substring(0, 8), // 只显示前8个字符保护隐私
    });

  } catch (error) {
    console.error('Trial file upload error:', error);
    return NextResponse.json(
      { success: false, error: 'File upload failed' },
      { status: 500 }
    );
  }
}

// 获取试用版本的上传限制信息
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      limits: {
        maxFileSize: TRIAL_LIMITS.maxFileSize,
        maxFileSizeMB: Math.round(TRIAL_LIMITS.maxFileSize / (1024 * 1024)),
        allowedTypes: ALLOWED_TYPES,
        plan: 'trial',
        dailyLimit: TRIAL_LIMITS.dailyLimit,
        message: `试用版本每天允许 ${TRIAL_LIMITS.dailyLimit} 次免费使用`,
        allowedOperations: TRIAL_LIMITS.allowedOperations,
      },
    });

  } catch (error) {
    console.error('Get trial upload limits error:', error);
    return NextResponse.json(
      { success: false, error: '获取上传限制失败' },
      { status: 500 }
    );
  }
}