import { NextRequest, NextResponse } from 'next/server';

interface QwenImageEditRequest {
  imageUrl: string;
  prompt: string;
  operation: 'text-edit' | 'background-replace';
}

interface QwenApiResponse {
  output?: {
    task_id?: string;
    results?: Array<{
      url: string;
    }>;
  };
  message?: string;
  code?: string;
}

// Qwen image editing API integration
export async function POST(request: NextRequest) {
  try {
    const { imageUrl, prompt, operation }: QwenImageEditRequest = await request.json();

    if (!imageUrl || !prompt || !operation) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const dashscopeApiKey = process.env.DASHSCOPE_API_KEY;
    if (!dashscopeApiKey || dashscopeApiKey === 'your_dashscope_api_key_here') {
      console.log('Qwen API key not configured, using enhanced simulation mode');
      return await simulateQwenProcessing(imageUrl, prompt, operation);
    }

    try {
      // Call DashScope Qwen Image Edit API for image editing
      const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/image-generation/generation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${dashscopeApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'qwen-image-edit',
          input: {
            prompt: buildQwenPrompt(prompt, operation),
            image_url: imageUrl,
            ...(operation === 'background-replace' && { 
              edit_type: 'inpainting' 
            }),
            ...(operation === 'text-edit' && { 
              edit_type: 'text_overlay' 
            })
          },
          parameters: {
            size: '1024*1024',
            n: 1,
            seed: Math.floor(Math.random() * 1000000)
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Qwen API error: ${response.status} ${response.statusText}`);
      }

      const data: QwenApiResponse = await response.json();
      
      if (data.output?.results && data.output.results.length > 0) {
        const resultUrl = data.output.results[0].url;
        
        return NextResponse.json({
          success: true,
          result: resultUrl,
          message: `AI ${operation} completed successfully`,
          provider: 'qwen-image-edit',
          task_id: data.output.task_id
        });
      } else {
        throw new Error(`No results returned from Qwen API: ${data.message || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('Qwen API processing error:', error);
      // Fallback to enhanced simulation
      return await simulateQwenProcessing(imageUrl, prompt, operation);
    }

  } catch (error) {
    console.error('Qwen image edit error:', error);
    return NextResponse.json(
      { success: false, error: 'Processing failed, please try again' },
      { status: 500 }
    );
  }
}

function buildQwenPrompt(userPrompt: string, operation: string): string {
  switch (operation) {
    case 'text-edit':
      return `Edit the text in this image: ${userPrompt}`;
    case 'background-replace':
      return `Replace the background with: ${userPrompt}`;
    default:
      return `Edit this image: ${userPrompt}`;
  }
}


async function simulateQwenProcessing(imageUrl: string, prompt: string, operation: string) {
  // Simulate API processing time
  await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000));
  
  // Create enhanced visual effects for different operations
  let filterEffect = '';
  let resultMessage = '';
  
  switch (operation) {
    case 'text-edit':
      filterEffect = 'brightness(1.1) contrast(1.2) saturate(1.1)';
      resultMessage = `✏️ AI text editing simulation complete! Prompt: "${prompt}"`;
      break;
    case 'background-replace':
      filterEffect = 'hue-rotate(30deg) saturate(1.3) brightness(1.05)';
      resultMessage = `🎨 AI background replacement simulation complete! Prompt: "${prompt}"`;
      break;
    default:
      filterEffect = 'saturate(1.2) brightness(1.05)';
      resultMessage = `🤖 AI processing simulation complete! Operation: ${operation}, Prompt: "${prompt}"`;
  }
  
  // Return processed URL with filter effects
  const processedUrl = `${imageUrl}?qwen_effect=${encodeURIComponent(filterEffect)}&operation=${operation}&provider=qwen&t=${Date.now()}`;
  
  return NextResponse.json({
    success: true,
    result: processedUrl,
    message: `${resultMessage} (Simulation mode - upgrade for real AI processing)`,
    provider: 'qwen-image-edit-simulation'
  });
}